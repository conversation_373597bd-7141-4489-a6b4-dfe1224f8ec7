import React, { useState, useCallback, useRef } from 'react'

interface DrawingToolProps {
  isActive: boolean
  strokeWidth: number
  color: string
  onDrawComplete: (points: { x: number; y: number }[], pageNumber: number) => void
}

interface DrawingPath {
  points: { x: number; y: number }[]
  pageNumber: number
}

const DrawingTool: React.FC<DrawingToolProps> = ({ 
  isActive, 
  strokeWidth, 
  color, 
  onDrawComplete 
}) => {
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentPath, setCurrentPath] = useState<DrawingPath | null>(null)
  const svgRef = useRef<SVGSVGElement>(null)

  const handleMouseDown = useCallback((e: React.MouseEvent, pageNumber: number) => {
    if (!isActive) return

    e.preventDefault()
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setIsDrawing(true)
    setCurrentPath({
      points: [{ x, y }],
      pageNumber
    })
  }, [isActive])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDrawing || !currentPath) return

    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setCurrentPath(prev => prev ? {
      ...prev,
      points: [...prev.points, { x, y }]
    } : null)
  }, [isDrawing, currentPath])

  const handleMouseUp = useCallback(() => {
    if (!isDrawing || !currentPath) return

    // Only save the drawing if it has enough points
    if (currentPath.points.length > 2) {
      onDrawComplete(currentPath.points, currentPath.pageNumber)
    }

    setIsDrawing(false)
    setCurrentPath(null)
  }, [isDrawing, currentPath, onDrawComplete])

  const generatePathData = (points: { x: number; y: number }[]) => {
    if (points.length < 2) return ''

    let pathData = `M ${points[0].x} ${points[0].y}`
    
    for (let i = 1; i < points.length; i++) {
      pathData += ` L ${points[i].x} ${points[i].y}`
    }

    return pathData
  }

  const renderCurrentPath = () => {
    if (!isDrawing || !currentPath || currentPath.points.length < 2) return null

    return (
      <path
        d={generatePathData(currentPath.points)}
        stroke={color}
        strokeWidth={strokeWidth}
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    )
  }

  return (
    <div
      className={`drawing-tool ${isActive ? 'active' : ''}`}
      onMouseDown={(e) => handleMouseDown(e, 1)} // TODO: Get actual page number
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp} // End drawing if mouse leaves the area
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        cursor: isActive ? 'crosshair' : 'default',
        zIndex: isActive ? 5 : -1
      }}
    >
      {isActive && (
        <svg
          ref={svgRef}
          className="drawing-svg"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none'
          }}
        >
          {renderCurrentPath()}
        </svg>
      )}
    </div>
  )
}

export default DrawingTool
