/**
 * PDF Worker Test Component
 * Simple component to test PDF.js worker initialization
 */

import React, { useState, useCallback } from 'react'
import * as pdfjsLib from 'pdfjs-dist'

interface WorkerTestResult {
  success: boolean
  workerUrl: string | null
  error: string | null
  testTime: number
}

const PDFWorkerTest: React.FC = () => {
  const [testResult, setTestResult] = useState<WorkerTestResult | null>(null)
  const [isRunning, setIsRunning] = useState(false)

  const testWorker = useCallback(async () => {
    setIsRunning(true)
    setTestResult(null)
    
    const startTime = performance.now()
    
    try {
      console.log('Testing PDF.js main thread mode...')

      // Use main thread mode (no workers)
      pdfjsLib.GlobalWorkerOptions.workerSrc = ''
      pdfjsLib.GlobalWorkerOptions.workerPort = null

      // Test with a minimal PDF
      const testPdfData = new Uint8Array([
        0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, 0x0a, // %PDF-1.4\n
        0x31, 0x20, 0x30, 0x20, 0x6f, 0x62, 0x6a, 0x0a,       // 1 0 obj\n
        0x3c, 0x3c, 0x2f, 0x54, 0x79, 0x70, 0x65, 0x2f,       // <</Type/
        0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f,       // Catalog/
        0x50, 0x61, 0x67, 0x65, 0x73, 0x20, 0x32, 0x20,       // Pages 2
        0x30, 0x20, 0x52, 0x3e, 0x3e, 0x0a, 0x65, 0x6e,       // 0 R>>\nen
        0x64, 0x6f, 0x62, 0x6a, 0x0a, 0x32, 0x20, 0x30,       // dobj\n2 0
        0x20, 0x6f, 0x62, 0x6a, 0x0a, 0x3c, 0x3c, 0x2f,       //  obj\n<</
        0x54, 0x79, 0x70, 0x65, 0x2f, 0x50, 0x61, 0x67,       // Type/Pag
        0x65, 0x73, 0x2f, 0x43, 0x6f, 0x75, 0x6e, 0x74,       // es/Count
        0x20, 0x30, 0x3e, 0x3e, 0x0a, 0x65, 0x6e, 0x64,       //  0>>\nend
        0x6f, 0x62, 0x6a, 0x0a, 0x78, 0x72, 0x65, 0x66,       // obj\nxref
        0x0a, 0x30, 0x20, 0x33, 0x0a, 0x30, 0x30, 0x30,       // \n0 3\n000
        0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x20,       // 0000000
        0x36, 0x35, 0x35, 0x33, 0x35, 0x20, 0x66, 0x20,       // 65535 f
        0x0a, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72,       // \ntrailer
        0x0a, 0x3c, 0x3c, 0x2f, 0x53, 0x69, 0x7a, 0x65,       // \n<</Size
        0x20, 0x33, 0x2f, 0x52, 0x6f, 0x6f, 0x74, 0x20,       //  3/Root
        0x31, 0x20, 0x30, 0x20, 0x52, 0x3e, 0x3e, 0x0a,       // 1 0 R>>\n
        0x73, 0x74, 0x61, 0x72, 0x74, 0x78, 0x72, 0x65,       // startxre
        0x66, 0x0a, 0x31, 0x31, 0x36, 0x0a, 0x25, 0x25,       // f\n116\n%%
        0x45, 0x4f, 0x46, 0x0a                                // EOF\n
      ])

      const loadingTask = pdfjsLib.getDocument({
        data: testPdfData,
        useWorkerFetch: false,
        isEvalSupported: false,
        useSystemFonts: true,
        verbosity: 0
      })

      const pdf = await loadingTask.promise

      if (pdf.numPages >= 0) {
        const testTime = performance.now() - startTime
        setTestResult({
          success: true,
          workerUrl: 'Main Thread Mode (No Worker)',
          error: null,
          testTime
        })
        setIsRunning(false)
        return
      }

      throw new Error('PDF.js main thread test failed')
    } catch (error: unknown) {
      const testTime = performance.now() - startTime
      setTestResult({
        success: false,
        workerUrl: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        testTime
      })
    } finally {
      setIsRunning(false)
    }
  }, [])

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      left: '10px', 
      background: 'white', 
      border: '1px solid #ccc', 
      padding: '10px', 
      borderRadius: '4px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      zIndex: 1000,
      fontSize: '12px',
      maxWidth: '300px'
    }}>
      <h4>PDF.js Worker Test</h4>
      
      <button 
        onClick={testWorker} 
        disabled={isRunning}
        style={{
          padding: '4px 8px',
          margin: '5px 0',
          background: isRunning ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '2px',
          cursor: isRunning ? 'not-allowed' : 'pointer'
        }}
      >
        {isRunning ? 'Testing...' : 'Test Worker'}
      </button>

      {testResult && (
        <div style={{ marginTop: '10px' }}>
          <div style={{ 
            color: testResult.success ? 'green' : 'red',
            fontWeight: 'bold'
          }}>
            {testResult.success ? '✅ SUCCESS' : '❌ FAILED'}
          </div>
          
          {testResult.success && testResult.workerUrl && (
            <div style={{ marginTop: '5px' }}>
              <strong>Worker URL:</strong><br />
              <span style={{ fontSize: '10px', wordBreak: 'break-all' }}>
                {testResult.workerUrl}
              </span>
            </div>
          )}
          
          {testResult.error && (
            <div style={{ marginTop: '5px', color: 'red' }}>
              <strong>Error:</strong><br />
              <span style={{ fontSize: '10px' }}>
                {testResult.error}
              </span>
            </div>
          )}
          
          <div style={{ marginTop: '5px' }}>
            <strong>Test Time:</strong> {testResult.testTime.toFixed(1)}ms
          </div>
        </div>
      )}
    </div>
  )
}

export default PDFWorkerTest
