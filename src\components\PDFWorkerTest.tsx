/**
 * PDF Worker Test Component
 * Simple component to test PDF.js worker initialization
 */

import React, { useState, useCallback } from 'react'
import * as pdfjsLib from 'pdfjs-dist'

interface WorkerTestResult {
  success: boolean
  workerUrl: string | null
  error: string | null
  testTime: number
}

const PDFWorkerTest: React.FC = () => {
  const [testResult, setTestResult] = useState<WorkerTestResult | null>(null)
  const [isRunning, setIsRunning] = useState(false)

  const testWorker = useCallback(async () => {
    setIsRunning(true)
    setTestResult(null)
    
    const startTime = performance.now()
    
    try {
      // Try multiple worker sources (local first, then CDN fallbacks)
      const workerSources = [
        '/pdf.worker.min.js', // Local file first (no CORS issues)
        '/pdf-worker/pdf.worker.min.js', // Alternative local path
        `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`,
        `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`,
        `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
      ]

      for (const workerSrc of workerSources) {
        try {
          console.log('Testing worker:', workerSrc)

          // Configure worker
          pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc

          // Test with a minimal PDF
          const testPdfData = new Uint8Array([
            0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, // %PDF-1.4
            0x0a, 0x25, 0x25, 0x45, 0x4f, 0x46, 0x0a        // \n%%EOF\n
          ])

          const loadingTask = pdfjsLib.getDocument({ data: testPdfData })
          const pdf = await loadingTask.promise

          if (pdf.numPages >= 0) {
            const testTime = performance.now() - startTime
            setTestResult({
              success: true,
              workerUrl: workerSrc,
              error: null,
              testTime
            })
            setIsRunning(false)
            return
          }
        } catch (error: unknown) {
          console.warn('Worker test failed for:', workerSrc, error)
          continue
        }
      }

      throw new Error('All worker sources failed')
    } catch (error: unknown) {
      const testTime = performance.now() - startTime
      setTestResult({
        success: false,
        workerUrl: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        testTime
      })
    } finally {
      setIsRunning(false)
    }
  }, [])

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      left: '10px', 
      background: 'white', 
      border: '1px solid #ccc', 
      padding: '10px', 
      borderRadius: '4px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      zIndex: 1000,
      fontSize: '12px',
      maxWidth: '300px'
    }}>
      <h4>PDF.js Worker Test</h4>
      
      <button 
        onClick={testWorker} 
        disabled={isRunning}
        style={{
          padding: '4px 8px',
          margin: '5px 0',
          background: isRunning ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '2px',
          cursor: isRunning ? 'not-allowed' : 'pointer'
        }}
      >
        {isRunning ? 'Testing...' : 'Test Worker'}
      </button>

      {testResult && (
        <div style={{ marginTop: '10px' }}>
          <div style={{ 
            color: testResult.success ? 'green' : 'red',
            fontWeight: 'bold'
          }}>
            {testResult.success ? '✅ SUCCESS' : '❌ FAILED'}
          </div>
          
          {testResult.success && testResult.workerUrl && (
            <div style={{ marginTop: '5px' }}>
              <strong>Worker URL:</strong><br />
              <span style={{ fontSize: '10px', wordBreak: 'break-all' }}>
                {testResult.workerUrl}
              </span>
            </div>
          )}
          
          {testResult.error && (
            <div style={{ marginTop: '5px', color: 'red' }}>
              <strong>Error:</strong><br />
              <span style={{ fontSize: '10px' }}>
                {testResult.error}
              </span>
            </div>
          )}
          
          <div style={{ marginTop: '5px' }}>
            <strong>Test Time:</strong> {testResult.testTime.toFixed(1)}ms
          </div>
        </div>
      )}
    </div>
  )
}

export default PDFWorkerTest
