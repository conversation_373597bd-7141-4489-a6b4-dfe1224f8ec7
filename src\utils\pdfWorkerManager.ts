import * as pdfjs from 'pdfjs-dist'

export interface WorkerTestResult {
  url: string
  success: boolean
  error?: string
  responseTime: number
  source: 'cdn' | 'local' | 'inline'
}

export interface WorkerStatus {
  isConfigured: boolean
  activeWorkerUrl: string | null
  testResults: WorkerTestResult[]
  browserSupport: {
    webWorkers: boolean
    blobUrls: boolean
    dynamicImports: boolean
  }
}

class PDFWorkerManager {
  private static instance: PDFWorkerManager
  private workerStatus: WorkerStatus = {
    isConfigured: false,
    activeWorkerUrl: null,
    testResults: [],
    browserSupport: {
      webWorkers: false,
      blobUrls: false,
      dynamicImports: false
    }
  }

  private debugLog = (message: string, data?: any) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    console.log(`[${timestamp}] PDFWorkerManager: ${message}`, data || '')
  }

  static getInstance(): PDFWorkerManager {
    if (!PDFWorkerManager.instance) {
      PDFWorkerManager.instance = new PDFWorkerManager()
    }
    return PDFWorkerManager.instance
  }

  // Test browser capabilities
  private async testBrowserSupport(): Promise<void> {
    this.debugLog('Testing browser support capabilities')

    // Test Web Workers
    this.workerStatus.browserSupport.webWorkers = typeof Worker !== 'undefined'

    // Test Blob URLs
    try {
      const blob = new Blob(['test'], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      this.workerStatus.browserSupport.blobUrls = url.startsWith('blob:')
      URL.revokeObjectURL(url)
    } catch {
      this.workerStatus.browserSupport.blobUrls = false
    }

    // Test Dynamic Imports
    try {
      // Simple dynamic import test - check if dynamic imports are supported
      this.workerStatus.browserSupport.dynamicImports = 'import' in globalThis
    } catch {
      this.workerStatus.browserSupport.dynamicImports = false
    }

    this.debugLog('Browser support results:', this.workerStatus.browserSupport)
  }

  // Test worker URL accessibility
  private async testWorkerUrl(url: string, source: 'cdn' | 'local' | 'inline', timeout = 3000): Promise<WorkerTestResult> {
    const startTime = Date.now()
    
    try {
      this.debugLog(`Testing worker URL: ${url}`)
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)
      
      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      })
      
      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime
      
      if (response.ok) {
        this.debugLog(`Worker URL accessible: ${url} (${responseTime}ms)`)
        return {
          url,
          success: true,
          responseTime,
          source
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime
      this.debugLog(`Worker URL failed: ${url} - ${error.message} (${responseTime}ms)`)
      return {
        url,
        success: false,
        error: error.message,
        responseTime,
        source
      }
    }
  }

  // Get prioritized list of worker URLs
  private getWorkerUrls(): Array<{ url: string; source: 'cdn' | 'local' | 'inline' }> {
    const version = pdfjs.version
    
    return [
      // Local files first (most reliable)
      { url: '/pdf-worker/pdf.worker.min.js', source: 'local' as const },
      { url: '/pdf-worker/pdf.worker.legacy.min.js', source: 'local' as const },
      
      // CDN fallbacks
      { url: `https://unpkg.com/pdfjs-dist@${version}/build/pdf.worker.min.js`, source: 'cdn' as const },
      { url: `https://cdn.jsdelivr.net/npm/pdfjs-dist@${version}/build/pdf.worker.min.js`, source: 'cdn' as const },
      { url: `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`, source: 'cdn' as const },
      
      // Legacy CDN fallbacks
      { url: `https://unpkg.com/pdfjs-dist@${version}/build/pdf.worker.legacy.min.js`, source: 'cdn' as const },
      { url: `https://cdn.jsdelivr.net/npm/pdfjs-dist@${version}/build/pdf.worker.legacy.min.js`, source: 'cdn' as const }
    ]
  }

  // Create inline worker as last resort
  private createInlineWorker(): string {
    this.debugLog('Creating inline worker as fallback')
    
    const workerCode = `
      // Minimal PDF.js worker implementation
      importScripts('https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js');
    `
    
    try {
      const blob = new Blob([workerCode], { type: 'application/javascript' })
      const workerUrl = URL.createObjectURL(blob)
      this.debugLog('Inline worker created successfully')
      return workerUrl
    } catch (error: any) {
      this.debugLog('Failed to create inline worker:', error.message)
      throw error
    }
  }

  // Initialize PDF.js worker with comprehensive fallback
  async initializeWorker(): Promise<WorkerStatus> {
    this.debugLog('Starting worker initialization process')
    
    // Test browser capabilities first
    await this.testBrowserSupport()
    
    if (!this.workerStatus.browserSupport.webWorkers) {
      throw new Error('Web Workers not supported in this browser')
    }

    const workerUrls = this.getWorkerUrls()
    this.workerStatus.testResults = []

    // Test all worker URLs
    for (const { url, source } of workerUrls) {
      const result = await this.testWorkerUrl(url, source)
      this.workerStatus.testResults.push(result)
      
      if (result.success) {
        try {
          // Test actual worker functionality
          pdfjs.GlobalWorkerOptions.workerSrc = url
          this.debugLog(`Configured PDF.js worker: ${url}`)
          
          // Quick functionality test
          await this.testWorkerFunctionality()
          
          this.workerStatus.isConfigured = true
          this.workerStatus.activeWorkerUrl = url
          this.debugLog('Worker initialization successful')
          return this.workerStatus
        } catch (error: any) {
          this.debugLog(`Worker functionality test failed for ${url}:`, error.message)
          continue
        }
      }
    }

    // Last resort: inline worker
    if (this.workerStatus.browserSupport.blobUrls) {
      try {
        const inlineWorkerUrl = this.createInlineWorker()
        const result = await this.testWorkerUrl(inlineWorkerUrl, 'inline')
        this.workerStatus.testResults.push(result)
        
        if (result.success) {
          pdfjs.GlobalWorkerOptions.workerSrc = inlineWorkerUrl
          await this.testWorkerFunctionality()
          
          this.workerStatus.isConfigured = true
          this.workerStatus.activeWorkerUrl = inlineWorkerUrl
          this.debugLog('Inline worker initialization successful')
          return this.workerStatus
        }
      } catch (error: any) {
        this.debugLog('Inline worker creation failed:', error.message)
      }
    }

    // Complete failure
    this.debugLog('All worker initialization attempts failed')
    throw new Error('Failed to initialize PDF.js worker: All fallback methods exhausted')
  }

  // Test basic worker functionality
  private async testWorkerFunctionality(): Promise<void> {
    this.debugLog('Testing worker functionality')
    
    try {
      // Create a minimal PDF for testing
      const testPdfData = new Uint8Array([
        0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, // %PDF-1.4
        0x0a, 0x31, 0x20, 0x30, 0x20, 0x6f, 0x62, 0x6a, // \n1 0 obj
        0x0a, 0x3c, 0x3c, 0x2f, 0x54, 0x79, 0x70, 0x65, // \n<</Type
        0x2f, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, // /Catalog
        0x2f, 0x50, 0x61, 0x67, 0x65, 0x73, 0x20, 0x32, // /Pages 2
        0x20, 0x30, 0x20, 0x52, 0x3e, 0x3e, 0x0a, 0x65, //  0 R>>\ne
        0x6e, 0x64, 0x6f, 0x62, 0x6a, 0x0a, 0x32, 0x20, // ndobj\n2 
        0x30, 0x20, 0x6f, 0x62, 0x6a, 0x0a, 0x3c, 0x3c, // 0 obj\n<<
        0x2f, 0x54, 0x79, 0x70, 0x65, 0x2f, 0x50, 0x61, // /Type/Pa
        0x67, 0x65, 0x73, 0x2f, 0x4b, 0x69, 0x64, 0x73, // ges/Kids
        0x5b, 0x33, 0x20, 0x30, 0x20, 0x52, 0x5d, 0x2f, // [3 0 R]/
        0x43, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x31, 0x3e, // Count 1>
        0x3e, 0x0a, 0x65, 0x6e, 0x64, 0x6f, 0x62, 0x6a, // >\nendobj
        0x0a, 0x78, 0x72, 0x65, 0x66, 0x0a, 0x30, 0x20, // \nxref\n0 
        0x34, 0x0a, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // 4\n000000
        0x30, 0x30, 0x30, 0x30, 0x20, 0x36, 0x35, 0x35, // 0000 655
        0x33, 0x35, 0x20, 0x66, 0x20, 0x0a, 0x74, 0x72, // 35 f \ntr
        0x61, 0x69, 0x6c, 0x65, 0x72, 0x0a, 0x3c, 0x3c, // ailer\n<<
        0x2f, 0x53, 0x69, 0x7a, 0x65, 0x20, 0x34, 0x2f, // /Size 4/
        0x52, 0x6f, 0x6f, 0x74, 0x20, 0x31, 0x20, 0x30, // Root 1 0
        0x20, 0x52, 0x3e, 0x3e, 0x0a, 0x73, 0x74, 0x61, //  R>>\nsta
        0x72, 0x74, 0x78, 0x72, 0x65, 0x66, 0x0a, 0x31, // rtxref\n1
        0x38, 0x34, 0x0a, 0x25, 0x25, 0x45, 0x4f, 0x46  // 84\n%%EOF
      ])
      
      const loadingTask = pdfjs.getDocument({ data: testPdfData })
      const pdf = await loadingTask.promise
      
      if (pdf.numPages >= 1) {
        this.debugLog('Worker functionality test passed')
      } else {
        throw new Error('Worker test returned invalid PDF')
      }
    } catch (error: any) {
      this.debugLog('Worker functionality test failed:', error.message)
      throw error
    }
  }

  // Get current worker status
  getStatus(): WorkerStatus {
    return { ...this.workerStatus }
  }

  // Reset worker configuration
  reset(): void {
    this.debugLog('Resetting worker configuration')
    this.workerStatus = {
      isConfigured: false,
      activeWorkerUrl: null,
      testResults: [],
      browserSupport: {
        webWorkers: false,
        blobUrls: false,
        dynamicImports: false
      }
    }
  }
}

export default PDFWorkerManager
