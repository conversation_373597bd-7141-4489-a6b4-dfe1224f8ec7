export interface BrowserInfo {
  name: string
  version: string
  engine: string
  isPrivacyFocused: boolean
  supportsBlob: boolean
  supportsPDFViewing: boolean
  hasTrackingProtection: boolean
  cspRestrictions: string[]
}

export interface CompatibilityResult {
  browserInfo: BrowserInfo
  recommendations: string[]
  fallbackMethods: string[]
  canUseBlobIframes: boolean
  canUseEmbedElements: boolean
  canUseObjectElements: boolean
  requiresUserAction: boolean
}

export class BrowserCompatibilityManager {
  private static instance: BrowserCompatibilityManager
  private browserInfo: BrowserInfo | null = null

  static getInstance(): BrowserCompatibilityManager {
    if (!BrowserCompatibilityManager.instance) {
      BrowserCompatibilityManager.instance = new BrowserCompatibilityManager()
    }
    return BrowserCompatibilityManager.instance
  }

  private debugLog = (message: string, data?: unknown) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    console.log(`[${timestamp}] BrowserCompatibility: ${message}`, data || '')
  }

  // Detect browser information
  private detectBrowser(): BrowserInfo {
    const userAgent = navigator.userAgent

    
    let name = 'Unknown'
    let version = 'Unknown'
    let engine = 'Unknown'
    let isPrivacyFocused = false
    let hasTrackingProtection = false
    let cspRestrictions: string[] = []

    // Brave Browser Detection
    if ((navigator as unknown as { brave?: { isBrave?: () => Promise<boolean> } }).brave?.isBrave) {
      name = 'Brave'
      isPrivacyFocused = true
      hasTrackingProtection = true
      cspRestrictions = ['blob-urls', 'inline-scripts', 'external-workers']
      
      const braveMatch = userAgent.match(/Chrome\/(\d+)/)
      version = braveMatch ? braveMatch[1] : 'Unknown'
      engine = 'Blink'
    }
    // Firefox Detection
    else if (userAgent.includes('Firefox')) {
      name = 'Firefox'
      engine = 'Gecko'
      
      const firefoxMatch = userAgent.match(/Firefox\/(\d+)/)
      version = firefoxMatch ? firefoxMatch[1] : 'Unknown'
      
      // Check for strict privacy mode
      if (userAgent.includes('Strict') || userAgent.includes('Private')) {
        isPrivacyFocused = true
        hasTrackingProtection = true
        cspRestrictions = ['tracking-protection', 'enhanced-privacy']
      }
    }
    // Safari Detection
    else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      name = 'Safari'
      engine = 'WebKit'
      
      const safariMatch = userAgent.match(/Version\/(\d+)/)
      version = safariMatch ? safariMatch[1] : 'Unknown'
      
      // Safari has Intelligent Tracking Prevention
      hasTrackingProtection = true
      cspRestrictions = ['intelligent-tracking-prevention', 'cross-site-tracking']
    }
    // Chrome Detection
    else if (userAgent.includes('Chrome')) {
      name = 'Chrome'
      engine = 'Blink'
      
      const chromeMatch = userAgent.match(/Chrome\/(\d+)/)
      version = chromeMatch ? chromeMatch[1] : 'Unknown'
      
      // Check for incognito mode (limited detection)
      if (userAgent.includes('Incognito')) {
        isPrivacyFocused = true
      }
    }
    // Edge Detection
    else if (userAgent.includes('Edg')) {
      name = 'Edge'
      engine = 'Blink'
      
      const edgeMatch = userAgent.match(/Edg\/(\d+)/)
      version = edgeMatch ? edgeMatch[1] : 'Unknown'
    }

    // Test blob URL support
    const supportsBlob = this.testBlobSupport()
    
    // Test PDF viewing support
    const supportsPDFViewing = this.testPDFSupport()

    return {
      name,
      version,
      engine,
      isPrivacyFocused,
      supportsBlob,
      supportsPDFViewing,
      hasTrackingProtection,
      cspRestrictions
    }
  }

  // Test blob URL support
  private testBlobSupport(): boolean {
    try {
      const blob = new Blob(['test'], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const isSupported = url.startsWith('blob:')
      URL.revokeObjectURL(url)
      return isSupported
    } catch {
      return false
    }
  }

  // Test PDF viewing support
  private testPDFSupport(): boolean {
    try {
      // Check for PDF MIME type support
      const mimeTypes = navigator.mimeTypes
      for (let i = 0; i < mimeTypes.length; i++) {
        if (mimeTypes[i].type === 'application/pdf') {
          return true
        }
      }
      
      // Check for PDF plugin
      const plugins = navigator.plugins
      for (let i = 0; i < plugins.length; i++) {
        const plugin = plugins[i]
        if (plugin.name.toLowerCase().includes('pdf') || 
            plugin.description.toLowerCase().includes('pdf')) {
          return true
        }
      }
      
      return false
    } catch {
      return false
    }
  }

  // Test blob URL accessibility in iframe
  async testBlobIframeCompatibility(testBlob: Blob): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const url = URL.createObjectURL(testBlob)
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        iframe.style.position = 'absolute'
        iframe.style.left = '-9999px'
        
        let resolved = false
        const cleanup = () => {
          if (!resolved) {
            resolved = true
            document.body.removeChild(iframe)
            URL.revokeObjectURL(url)
          }
        }

        iframe.onload = () => {
          this.debugLog('Blob iframe test: SUCCESS')
          cleanup()
          resolve(true)
        }

        iframe.onerror = () => {
          this.debugLog('Blob iframe test: FAILED')
          cleanup()
          resolve(false)
        }

        // Timeout after 3 seconds
        setTimeout(() => {
          if (!resolved) {
            this.debugLog('Blob iframe test: TIMEOUT')
            cleanup()
            resolve(false)
          }
        }, 3000)

        document.body.appendChild(iframe)
        iframe.src = url
      } catch (error) {
        this.debugLog('Blob iframe test: ERROR', error)
        resolve(false)
      }
    })
  }

  // Get comprehensive compatibility analysis
  async analyzeCompatibility(testFile?: File): Promise<CompatibilityResult> {
    this.debugLog('Starting browser compatibility analysis')
    
    if (!this.browserInfo) {
      this.browserInfo = this.detectBrowser()
    }

    const recommendations: string[] = []
    const fallbackMethods: string[] = []
    let canUseBlobIframes = this.browserInfo.supportsBlob
    let canUseEmbedElements = true
    let canUseObjectElements = true
    let requiresUserAction = false

    // Test blob iframe compatibility if we have a test file
    if (testFile && this.browserInfo.supportsBlob) {
      canUseBlobIframes = await this.testBlobIframeCompatibility(testFile)
    }

    // Browser-specific recommendations
    switch (this.browserInfo.name) {
      case 'Brave':
        recommendations.push('Brave blocks blob URLs in iframes by default')
        recommendations.push('Consider disabling Brave Shields for this site')
        recommendations.push('Use embed elements or direct download as fallback')
        canUseBlobIframes = false
        requiresUserAction = true
        fallbackMethods.push('embed-element', 'object-element', 'direct-download')
        break

      case 'Firefox':
        if (this.browserInfo.hasTrackingProtection) {
          recommendations.push('Firefox Enhanced Tracking Protection may block blob URLs')
          recommendations.push('Try disabling Enhanced Tracking Protection for this site')
          canUseBlobIframes = false
        }
        fallbackMethods.push('embed-element', 'object-element')
        break

      case 'Safari':
        recommendations.push('Safari has Intelligent Tracking Prevention')
        recommendations.push('Blob URLs may be restricted in some contexts')
        if (parseInt(this.browserInfo.version) < 14) {
          canUseBlobIframes = false
          recommendations.push('Consider updating Safari for better PDF support')
        }
        fallbackMethods.push('object-element', 'embed-element')
        break

      case 'Chrome':
        if (this.browserInfo.isPrivacyFocused) {
          recommendations.push('Incognito mode may restrict blob URL access')
          canUseBlobIframes = false
        }
        fallbackMethods.push('embed-element', 'object-element')
        break

      case 'Edge':
        // Edge generally has good compatibility
        fallbackMethods.push('embed-element', 'object-element')
        break

      default:
        recommendations.push('Unknown browser - compatibility may vary')
        fallbackMethods.push('embed-element', 'object-element', 'direct-download')
        break
    }

    // General recommendations
    if (!this.browserInfo.supportsPDFViewing) {
      recommendations.push('Browser may not have built-in PDF support')
      recommendations.push('Consider using Canvas-based PDF renderer')
      canUseEmbedElements = false
      canUseObjectElements = false
    }

    if (this.browserInfo.cspRestrictions.length > 0) {
      recommendations.push(`CSP restrictions detected: ${this.browserInfo.cspRestrictions.join(', ')}`)
    }

    this.debugLog('Compatibility analysis complete', {
      canUseBlobIframes,
      canUseEmbedElements,
      canUseObjectElements,
      requiresUserAction
    })

    return {
      browserInfo: this.browserInfo,
      recommendations,
      fallbackMethods,
      canUseBlobIframes,
      canUseEmbedElements,
      canUseObjectElements,
      requiresUserAction
    }
  }

  // Get browser-specific instructions
  getBrowserInstructions(browserName: string): string[] {
    const instructions: string[] = []

    switch (browserName.toLowerCase()) {
      case 'brave':
        instructions.push('1. Click the Brave Shield icon in the address bar')
        instructions.push('2. Toggle "Shields Down" for this site')
        instructions.push('3. Refresh the page and try again')
        instructions.push('Alternative: Use the Canvas PDF Viewer instead')
        break

      case 'firefox':
        instructions.push('1. Click the shield icon in the address bar')
        instructions.push('2. Click "Turn off Enhanced Tracking Protection"')
        instructions.push('3. Refresh the page and try again')
        instructions.push('Alternative: Go to about:config and set privacy.trackingprotection.enabled to false')
        break

      case 'safari':
        instructions.push('1. Go to Safari > Preferences > Privacy')
        instructions.push('2. Uncheck "Prevent cross-site tracking"')
        instructions.push('3. Refresh the page and try again')
        instructions.push('Alternative: Use the Canvas PDF Viewer for better compatibility')
        break

      default:
        instructions.push('1. Try disabling privacy/tracking protection for this site')
        instructions.push('2. Ensure JavaScript is enabled')
        instructions.push('3. Try using a different browser')
        instructions.push('4. Use the Canvas PDF Viewer as an alternative')
        break
    }

    return instructions
  }

  // Reset cached browser info
  reset(): void {
    this.browserInfo = null
  }
}

export default BrowserCompatibilityManager
