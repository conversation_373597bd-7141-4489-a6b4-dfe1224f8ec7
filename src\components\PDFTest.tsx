import React, { useState } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'

// Configure worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`

const PDFTest: React.FC = () => {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages)
    console.log('Test PDF loaded successfully with', numPages, 'pages')
  }

  function onDocumentLoadError(error: any) {
    console.error('Test PDF load error:', error)
  }

  return (
    <div style={{ padding: '20px', border: '2px solid #ccc', margin: '20px' }}>
      <h3>PDF Test Component</h3>
      <p>Testing react-pdf with a simple PDF</p>
      
      <Document
        file="/test.pdf"
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={onDocumentLoadError}
        loading={<div>Loading test PDF...</div>}
        error={<div>Failed to load test PDF</div>}
      >
        <Page 
          pageNumber={pageNumber} 
          renderTextLayer={false}
          renderAnnotationLayer={false}
          loading={<div>Loading page...</div>}
          error={<div>Failed to load page</div>}
        />
      </Document>
      
      {numPages > 0 && (
        <p>Page {pageNumber} of {numPages}</p>
      )}
    </div>
  )
}

export default PDFTest
