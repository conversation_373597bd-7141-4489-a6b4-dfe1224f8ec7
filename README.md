# PDF Editor

A modern, client-side PDF editor built with React and TypeScript that runs entirely in the browser without requiring a backend server or database.

## Features

### Core Functionality
- **PDF File Upload**: Drag-and-drop and file picker interfaces
- **Text Editing**: Add, modify, and delete text elements with customizable fonts, sizes, and colors
- **Annotations**: Add comments, highlights, and markup tools
- **Drawing Tools**: Basic sketching and drawing functionality
- **Image Insertion**: Add images to PDF documents with positioning and rotation controls
- **Save/Download**: Export modified PDFs with all changes applied

### User Interface
- **Modern Design**: Clean, intuitive interface with responsive layout
- **Toolbar**: Comprehensive editing tools with visual feedback
- **Status Bar**: Real-time information about current tool, page, zoom level, and edit count
- **History Panel**: Visual undo/redo history with operation tracking
- **Loading States**: Progress indicators and loading overlays
- **Error Handling**: Comprehensive error boundaries and user feedback

### Technical Features
- **Client-Side Only**: All processing happens in the browser
- **No Backend Required**: No server or database dependencies
- **Performance Optimized**: Efficient rendering and memory management
- **Browser Compatible**: Works in modern browsers with feature detection
- **TypeScript**: Full type safety and better development experience

## Technology Stack

- **React 18** - UI framework
- **TypeScript** - Type safety and better development experience
- **Vite** - Fast build tool and development server
- **PDF-lib** - PDF manipulation and editing
- **React-PDF** - PDF viewing and rendering
- **Lucide React** - Modern icon library

## Getting Started

### Prerequisites
- Node.js 16+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pdf-editor
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Usage

### Basic Workflow

1. **Upload PDF**: Drag and drop a PDF file or click to browse
2. **Select Tool**: Choose from text, highlight, comment, image, or draw tools
3. **Edit Document**: Click on the PDF to add elements or make changes
4. **Save Changes**: Use the save button to download the modified PDF

### Available Tools

- **Select**: Default tool for selecting and moving elements
- **Text**: Add text with customizable font, size, and color
- **Highlight**: Create highlighted regions by dragging
- **Comment**: Add comment annotations at specific points
- **Image**: Insert images with positioning and rotation controls
- **Draw**: Free-hand drawing and sketching
- **Erase**: Remove elements (planned feature)

### Keyboard Shortcuts

- `Ctrl+Z` / `Cmd+Z`: Undo
- `Ctrl+Y` / `Cmd+Y`: Redo
- `Ctrl+S` / `Cmd+S`: Save
- `Escape`: Cancel current operation

## Architecture

### Component Structure
```
src/
├── components/           # React components
│   ├── FileUpload.tsx   # File upload interface
│   ├── PDFEditor.tsx    # Main editor component
│   ├── PDFViewer.tsx    # PDF rendering component
│   ├── Toolbar.tsx      # Editing tools
│   ├── StatusBar.tsx    # Status information
│   ├── modals/          # Modal components
│   └── ...
├── services/            # Business logic
│   ├── pdfEditingService.ts  # PDF manipulation
│   └── undoRedoService.ts    # History management
├── utils/               # Utility functions
└── App.tsx             # Main application
```

### Key Services

- **PDFEditingService**: Handles all PDF manipulation operations
- **UndoRedoService**: Manages operation history and undo/redo functionality
- **Performance utilities**: Optimization and monitoring tools

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Required Browser Features
- File API
- Canvas API
- Web Workers (optional, for performance)
- IndexedDB (for caching)
- Local Storage

## Performance Considerations

- **Memory Management**: Efficient handling of large PDF files
- **Lazy Loading**: Components loaded on demand
- **Debounced Operations**: Optimized user interactions
- **Image Compression**: Automatic optimization of inserted images
- **Caching**: Smart caching of rendered pages

## Security

- **Client-Side Only**: No data leaves the user's browser
- **No Server Communication**: All processing is local
- **File Validation**: Input validation for uploaded files
- **Error Boundaries**: Graceful error handling

## Limitations

- **PDF Compatibility**: Some complex PDF features may not be supported
- **File Size**: Large files may impact performance
- **Browser Memory**: Limited by browser memory constraints
- **Text Extraction**: Limited ability to modify existing PDF text

## License

This project is licensed under the MIT License.
