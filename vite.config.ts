import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    include: ['react-pdf', 'pdfjs-dist']
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-pdf': ['react-pdf'],
          'pdfjs-dist': ['pdfjs-dist']
        }
      }
    }
  },
  server: {
    headers: {
      // Remove restrictive CORS headers that block blob URLs
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
      // Allow blob URLs in iframes
      'Content-Security-Policy': "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; frame-src 'self' blob: data:; object-src 'self' blob: data:;",
    }
  },
  define: {
    global: 'globalThis',
  }
})
