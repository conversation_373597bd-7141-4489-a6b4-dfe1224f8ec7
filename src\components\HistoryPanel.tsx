import React from 'react'
import { Clock, Undo, Redo } from 'lucide-react'

// Define UndoRedoState locally to avoid import issues
interface TextElement {
  id: string
  x: number
  y: number
  text: string
  fontSize: number
  color: string
  fontFamily: string
  pageNumber: number
}

interface HighlightElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  color: string
  pageNumber: number
}

interface CommentElement {
  id: string
  x: number
  y: number
  text: string
  pageNumber: number
}

interface DrawElement {
  id: string
  points: { x: number; y: number }[]
  strokeWidth: number
  color: string
  pageNumber: number
}

interface ImageElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  imageData: string
  rotation: number
  pageNumber: number
}

type EditElement = TextElement | HighlightElement | CommentElement | DrawElement | ImageElement

interface UndoRedoState {
  elements: EditElement[]
  timestamp: number
  description: string
}

interface HistoryPanelProps {
  isOpen: boolean
  undoHistory: UndoRedoState[]
  redoHistory: UndoRedoState[]
  onUndo: () => void
  onRedo: () => void
  canUndo: boolean
  canRedo: boolean
  onClose: () => void
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({
  isOpen,
  undoHistory,
  redoHistory,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  onClose
}) => {
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })
  }

  if (!isOpen) return null

  return (
    <div className="history-panel">
      <div className="history-header">
        <div className="header-title">
          <Clock size={16} />
          <h3>Edit History</h3>
        </div>
        <button onClick={onClose} className="close-button">
          ×
        </button>
      </div>

      <div className="history-content">
        <div className="history-actions">
          <button
            onClick={onUndo}
            disabled={!canUndo}
            className="history-action-button"
            title={canUndo ? `Undo: ${undoHistory[undoHistory.length - 1]?.description}` : 'Nothing to undo'}
          >
            <Undo size={16} />
            Undo
          </button>
          <button
            onClick={onRedo}
            disabled={!canRedo}
            className="history-action-button"
            title={canRedo ? `Redo: ${redoHistory[redoHistory.length - 1]?.description}` : 'Nothing to redo'}
          >
            <Redo size={16} />
            Redo
          </button>
        </div>

        <div className="history-list">
          <div className="history-section">
            <h4>Recent Actions</h4>
            {undoHistory.length === 0 ? (
              <div className="history-empty">No actions yet</div>
            ) : (
              <div className="history-items">
                {undoHistory.slice(-10).reverse().map((state, index) => (
                  <div
                    key={state.timestamp}
                    className={`history-item ${index === 0 ? 'current' : ''}`}
                  >
                    <div className="history-item-content">
                      <span className="history-description">{state.description}</span>
                      <span className="history-time">{formatTime(state.timestamp)}</span>
                    </div>
                    <div className="history-item-count">
                      {state.elements.length} element{state.elements.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {redoHistory.length > 0 && (
            <div className="history-section">
              <h4>Available Redo Actions</h4>
              <div className="history-items">
                {redoHistory.slice(-5).reverse().map((state) => (
                  <div key={state.timestamp} className="history-item redo-item">
                    <div className="history-item-content">
                      <span className="history-description">{state.description}</span>
                      <span className="history-time">{formatTime(state.timestamp)}</span>
                    </div>
                    <div className="history-item-count">
                      {state.elements.length} element{state.elements.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="history-stats">
          <div className="stat-item">
            <span className="stat-label">Total Actions:</span>
            <span className="stat-value">{undoHistory.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Available Redos:</span>
            <span className="stat-value">{redoHistory.length}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HistoryPanel
