import React, { useState, useEffect, useRef } from 'react'
import { X, Check } from 'lucide-react'

interface TextInputModalProps {
  isOpen: boolean
  x: number
  y: number
  initialText?: string
  fontSize?: number
  color?: string
  fontFamily?: string
  onSave: (text: string, fontSize: number, color: string, fontFamily: string) => void
  onCancel: () => void
}

const TextInputModal: React.FC<TextInputModalProps> = ({
  isOpen,
  x,
  y,
  initialText = '',
  fontSize = 16,
  color = '#000000',
  fontFamily = 'Arial',
  onSave,
  onCancel
}) => {
  const [text, setText] = useState(initialText)
  const [currentFontSize, setCurrentFontSize] = useState(fontSize)
  const [currentColor, setCurrentColor] = useState(color)
  const [currentFontFamily, setCurrentFontFamily] = useState(fontFamily)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus()
      textareaRef.current.select()
    }
  }, [isOpen])

  useEffect(() => {
    setText(initialText)
    setCurrentFontSize(fontSize)
    setCurrentColor(color)
    setCurrentFontFamily(fontFamily)
  }, [initialText, fontSize, color, fontFamily])

  const handleSave = () => {
    if (text.trim()) {
      onSave(text.trim(), currentFontSize, currentColor, currentFontFamily)
    } else {
      onCancel()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  if (!isOpen) return null

  return (
    <>
      <div className="modal-overlay" onClick={onCancel} />
      <div
        className="text-input-modal"
        style={{
          position: 'absolute',
          left: Math.min(x, (typeof window !== 'undefined' ? window.innerWidth : 1200) - 320),
          top: Math.min(y, (typeof window !== 'undefined' ? window.innerHeight : 800) - 300),
          zIndex: 1000
        }}
      >
        <div className="modal-header">
          <h3>Add Text</h3>
          <button onClick={onCancel} className="close-button">
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          <div className="text-input-section">
            <label>Text:</label>
            <textarea
              ref={textareaRef}
              value={text}
              onChange={(e) => setText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter your text here..."
              rows={3}
              className="text-input"
            />
          </div>

          <div className="text-settings-grid">
            <div className="setting-group">
              <label>Font Size:</label>
              <input
                type="number"
                min="8"
                max="72"
                value={currentFontSize}
                onChange={(e) => setCurrentFontSize(parseInt(e.target.value) || 16)}
                className="setting-input"
              />
            </div>

            <div className="setting-group">
              <label>Color:</label>
              <input
                type="color"
                value={currentColor}
                onChange={(e) => setCurrentColor(e.target.value)}
                className="color-input"
              />
            </div>

            <div className="setting-group">
              <label>Font:</label>
              <select
                value={currentFontFamily}
                onChange={(e) => setCurrentFontFamily(e.target.value)}
                className="setting-select"
              >
                <option value="Arial">Arial</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Courier New">Courier New</option>
                <option value="Helvetica">Helvetica</option>
              </select>
            </div>
          </div>

          <div className="text-preview">
            <label>Preview:</label>
            <div 
              className="preview-text"
              style={{
                fontSize: `${currentFontSize}px`,
                color: currentColor,
                fontFamily: currentFontFamily
              }}
            >
              {text || 'Sample text'}
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onCancel} className="cancel-button">
            Cancel
          </button>
          <button onClick={handleSave} className="save-button">
            <Check size={16} />
            Add Text
          </button>
        </div>
      </div>
    </>
  )
}

export default TextInputModal
