import React, { useState, useRef, useCallback } from 'react'
import { <PERSON>, Highlighter, Edit3, MessageSquare, Square, Circle } from 'lucide-react'

interface EditingAnnotation {
  id: string
  type: 'text' | 'highlight' | 'draw' | 'comment' | 'image' | 'shape'
  pageNumber: number
  x: number
  y: number
  width?: number
  height?: number
  content?: string
  color?: string
  fontSize?: number
  strokeWidth?: number
  points?: { x: number; y: number }[]
  shapeType?: 'rectangle' | 'circle' | 'arrow'
  timestamp: number
}

interface CanvasEditingOverlayProps {
  pageNumber: number
  canvasWidth: number
  canvasHeight: number
  scale: number
  onAnnotationAdd: (annotation: EditingAnnotation) => void
  annotations: EditingAnnotation[]
  isActive: boolean
}

type EditingTool = 'select' | 'text' | 'highlight' | 'draw' | 'comment' | 'image' | 'rectangle' | 'circle' | 'arrow'

const CanvasEditingOverlay: React.FC<CanvasEditingOverlayProps> = ({
  pageNumber,
  canvasWidth,
  canvasHeight,
  scale,
  onAnnotationAdd,
  annotations,
  isActive
}) => {
  const [currentTool, setCurrentTool] = useState<EditingTool>('select')
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>([])
  const [selectedColor, setSelectedColor] = useState('#ff0000')
  const [strokeWidth, setStrokeWidth] = useState(2)
  const [fontSize, setFontSize] = useState(16)
  const [showTextInput, setShowTextInput] = useState(false)
  const [textInputPosition, setTextInputPosition] = useState({ x: 0, y: 0 })
  const [textInputValue, setTextInputValue] = useState('')

  const overlayRef = useRef<HTMLDivElement>(null)
  const svgRef = useRef<SVGSVGElement>(null)

  // Get relative coordinates within the overlay
  const getRelativeCoordinates = useCallback((event: React.MouseEvent) => {
    if (!overlayRef.current) return { x: 0, y: 0 }
    
    const rect = overlayRef.current.getBoundingClientRect()
    return {
      x: (event.clientX - rect.left) / scale,
      y: (event.clientY - rect.top) / scale
    }
  }, [scale])

  // Handle mouse down events
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (!isActive) return
    
    const coords = getRelativeCoordinates(event)
    
    switch (currentTool) {
      case 'text':
        setTextInputPosition(coords)
        setShowTextInput(true)
        break
        
      case 'draw':
        setIsDrawing(true)
        setCurrentPath([coords])
        break
        
      case 'highlight':
        // Start highlight selection
        setIsDrawing(true)
        setCurrentPath([coords])
        break
        
      case 'comment': {
        // Add comment annotation
        const commentAnnotation: EditingAnnotation = {
          id: `comment-${Date.now()}`,
          type: 'comment',
          pageNumber,
          x: coords.x,
          y: coords.y,
          content: 'New Comment',
          color: selectedColor,
          timestamp: Date.now()
        }
        onAnnotationAdd(commentAnnotation)
        break
      }
        
      case 'rectangle':
      case 'circle':
      case 'arrow':
        setIsDrawing(true)
        setCurrentPath([coords])
        break
    }
  }, [isActive, currentTool, pageNumber, selectedColor, getRelativeCoordinates, onAnnotationAdd])

  // Handle mouse move events
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isDrawing || !isActive) return
    
    const coords = getRelativeCoordinates(event)
    
    if (currentTool === 'draw' || currentTool === 'highlight') {
      setCurrentPath(prev => [...prev, coords])
    } else if (['rectangle', 'circle', 'arrow'].includes(currentTool)) {
      setCurrentPath(prev => [prev[0], coords])
    }
  }, [isDrawing, isActive, currentTool, getRelativeCoordinates])

  // Handle mouse up events
  const handleMouseUp = useCallback(() => {
    if (!isDrawing || !isActive) return
    
    setIsDrawing(false)
    
    if (currentPath.length > 1) {
      let annotation: EditingAnnotation
      
      switch (currentTool) {
        case 'draw':
          annotation = {
            id: `draw-${Date.now()}`,
            type: 'draw',
            pageNumber,
            x: currentPath[0].x,
            y: currentPath[0].y,
            points: currentPath,
            color: selectedColor,
            strokeWidth,
            timestamp: Date.now()
          }
          break
          
        case 'highlight':
          annotation = {
            id: `highlight-${Date.now()}`,
            type: 'highlight',
            pageNumber,
            x: Math.min(currentPath[0].x, currentPath[currentPath.length - 1].x),
            y: Math.min(currentPath[0].y, currentPath[currentPath.length - 1].y),
            width: Math.abs(currentPath[currentPath.length - 1].x - currentPath[0].x),
            height: Math.abs(currentPath[currentPath.length - 1].y - currentPath[0].y),
            color: selectedColor,
            timestamp: Date.now()
          }
          break
          
        case 'rectangle':
        case 'circle':
        case 'arrow':
          annotation = {
            id: `shape-${Date.now()}`,
            type: 'shape',
            pageNumber,
            x: Math.min(currentPath[0].x, currentPath[1].x),
            y: Math.min(currentPath[0].y, currentPath[1].y),
            width: Math.abs(currentPath[1].x - currentPath[0].x),
            height: Math.abs(currentPath[1].y - currentPath[0].y),
            shapeType: currentTool as 'rectangle' | 'circle' | 'arrow',
            color: selectedColor,
            strokeWidth,
            timestamp: Date.now()
          }
          break
          
        default:
          return
      }
      
      onAnnotationAdd(annotation)
    }
    
    setCurrentPath([])
  }, [isDrawing, isActive, currentPath, currentTool, pageNumber, selectedColor, strokeWidth, onAnnotationAdd])

  // Handle text input submission
  const handleTextSubmit = useCallback(() => {
    if (textInputValue.trim()) {
      const textAnnotation: EditingAnnotation = {
        id: `text-${Date.now()}`,
        type: 'text',
        pageNumber,
        x: textInputPosition.x,
        y: textInputPosition.y,
        content: textInputValue,
        color: selectedColor,
        fontSize,
        timestamp: Date.now()
      }
      onAnnotationAdd(textAnnotation)
    }
    
    setShowTextInput(false)
    setTextInputValue('')
  }, [textInputValue, textInputPosition, pageNumber, selectedColor, fontSize, onAnnotationAdd])

  // Render SVG path for drawing
  const renderCurrentPath = () => {
    if (currentPath.length < 2) return null
    
    switch (currentTool) {
      case 'draw': {
        const pathData = currentPath.reduce((path, point, index) => {
          return path + (index === 0 ? `M ${point.x} ${point.y}` : ` L ${point.x} ${point.y}`)
        }, '')
        return (
          <path
            d={pathData}
            stroke={selectedColor}
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        )
      }
        
      case 'highlight': {
        const [start, end] = [currentPath[0], currentPath[currentPath.length - 1]]
        return (
          <rect
            x={Math.min(start.x, end.x)}
            y={Math.min(start.y, end.y)}
            width={Math.abs(end.x - start.x)}
            height={Math.abs(end.y - start.y)}
            fill={selectedColor}
            opacity={0.3}
          />
        )
      }
        
      case 'rectangle': {
        const [rectStart, rectEnd] = [currentPath[0], currentPath[1]]
        return (
          <rect
            x={Math.min(rectStart.x, rectEnd.x)}
            y={Math.min(rectStart.y, rectEnd.y)}
            width={Math.abs(rectEnd.x - rectStart.x)}
            height={Math.abs(rectEnd.y - rectStart.y)}
            stroke={selectedColor}
            strokeWidth={strokeWidth}
            fill="none"
          />
        )
      }
        
      case 'circle': {
        const [circleStart, circleEnd] = [currentPath[0], currentPath[1]]
        const radius = Math.sqrt(
          Math.pow(circleEnd.x - circleStart.x, 2) + Math.pow(circleEnd.y - circleStart.y, 2)
        ) / 2
        return (
          <circle
            cx={(circleStart.x + circleEnd.x) / 2}
            cy={(circleStart.y + circleEnd.y) / 2}
            r={radius}
            stroke={selectedColor}
            strokeWidth={strokeWidth}
            fill="none"
          />
        )
      }
        
      default:
        return null
    }
  }

  if (!isActive) return null

  return (
    <div
      ref={overlayRef}
      className="canvas-editing-overlay"
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: canvasWidth * scale,
        height: canvasHeight * scale,
        cursor: currentTool === 'select' ? 'default' : 'crosshair',
        pointerEvents: isActive ? 'auto' : 'none'
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      {/* Editing Tools */}
      <div className="editing-tools">
        <button
          onClick={() => setCurrentTool('select')}
          className={`tool-btn ${currentTool === 'select' ? 'active' : ''}`}
          title="Select"
        >
          Select
        </button>
        <button
          onClick={() => setCurrentTool('text')}
          className={`tool-btn ${currentTool === 'text' ? 'active' : ''}`}
          title="Add Text"
        >
          <Type size={16} />
        </button>
        <button
          onClick={() => setCurrentTool('highlight')}
          className={`tool-btn ${currentTool === 'highlight' ? 'active' : ''}`}
          title="Highlight"
        >
          <Highlighter size={16} />
        </button>
        <button
          onClick={() => setCurrentTool('draw')}
          className={`tool-btn ${currentTool === 'draw' ? 'active' : ''}`}
          title="Draw"
        >
          <Edit3 size={16} />
        </button>
        <button
          onClick={() => setCurrentTool('comment')}
          className={`tool-btn ${currentTool === 'comment' ? 'active' : ''}`}
          title="Add Comment"
        >
          <MessageSquare size={16} />
        </button>
        <button
          onClick={() => setCurrentTool('rectangle')}
          className={`tool-btn ${currentTool === 'rectangle' ? 'active' : ''}`}
          title="Rectangle"
        >
          <Square size={16} />
        </button>
        <button
          onClick={() => setCurrentTool('circle')}
          className={`tool-btn ${currentTool === 'circle' ? 'active' : ''}`}
          title="Circle"
        >
          <Circle size={16} />
        </button>
      </div>

      {/* Color and Style Controls */}
      <div className="style-controls">
        <input
          type="color"
          value={selectedColor}
          onChange={(e) => setSelectedColor(e.target.value)}
          className="color-picker"
        />
        <input
          type="range"
          min="1"
          max="10"
          value={strokeWidth}
          onChange={(e) => setStrokeWidth(parseInt(e.target.value))}
          className="stroke-width"
          title="Stroke Width"
        />
        <input
          type="range"
          min="8"
          max="48"
          value={fontSize}
          onChange={(e) => setFontSize(parseInt(e.target.value))}
          className="font-size"
          title="Font Size"
        />
      </div>

      {/* SVG Overlay for Annotations */}
      <svg
        ref={svgRef}
        width={canvasWidth * scale}
        height={canvasHeight * scale}
        style={{ position: 'absolute', top: 0, left: 0, pointerEvents: 'none' }}
      >
        {/* Render existing annotations */}
        {annotations
          .filter(annotation => annotation.pageNumber === pageNumber)
          .map(annotation => {
            switch (annotation.type) {
              case 'highlight':
                return (
                  <rect
                    key={annotation.id}
                    x={annotation.x * scale}
                    y={annotation.y * scale}
                    width={(annotation.width || 0) * scale}
                    height={(annotation.height || 0) * scale}
                    fill={annotation.color}
                    opacity={0.3}
                  />
                )
              case 'draw':
                if (annotation.points) {
                  const pathData = annotation.points.reduce((path, point, index) => {
                    return path + (index === 0 ? `M ${point.x * scale} ${point.y * scale}` : ` L ${point.x * scale} ${point.y * scale}`)
                  }, '')
                  return (
                    <path
                      key={annotation.id}
                      d={pathData}
                      stroke={annotation.color}
                      strokeWidth={(annotation.strokeWidth || 2) * scale}
                      fill="none"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  )
                }
                break
              default:
                return null
            }
          })}
        
        {/* Render current drawing */}
        {renderCurrentPath()}
      </svg>

      {/* Text Input Modal */}
      {showTextInput && (
        <div
          className="text-input-modal"
          style={{
            position: 'absolute',
            left: textInputPosition.x * scale,
            top: textInputPosition.y * scale,
            background: 'white',
            border: '1px solid #ccc',
            borderRadius: '4px',
            padding: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            zIndex: 1000
          }}
        >
          <input
            type="text"
            value={textInputValue}
            onChange={(e) => setTextInputValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleTextSubmit()}
            placeholder="Enter text..."
            autoFocus
            style={{
              border: 'none',
              outline: 'none',
              fontSize: `${fontSize}px`,
              color: selectedColor
            }}
          />
          <div style={{ marginTop: '4px', display: 'flex', gap: '4px' }}>
            <button onClick={handleTextSubmit} style={{ fontSize: '12px', padding: '2px 6px' }}>
              Add
            </button>
            <button onClick={() => setShowTextInput(false)} style={{ fontSize: '12px', padding: '2px 6px' }}>
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default CanvasEditingOverlay
