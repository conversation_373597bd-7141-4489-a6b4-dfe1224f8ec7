/* Canvas PDF Editor Styles */

/* Workflow Test Panel Styles */
.workflow-test-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.test-panel-content {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.test-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.test-panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: #f3f4f6;
}

.test-panel-body {
  padding: 20px;
}

.test-description {
  margin-bottom: 20px;
}

.test-description p {
  margin-bottom: 10px;
  color: #374151;
}

.test-description ul {
  margin: 0;
  padding-left: 20px;
  color: #6b7280;
}

.test-description li {
  margin-bottom: 4px;
}

.test-controls {
  margin-bottom: 20px;
}

.run-test-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.run-test-button:hover:not(:disabled) {
  background: #2563eb;
}

.run-test-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.test-results {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.test-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  font-weight: 500;
}

.test-summary.success {
  background: #f0fdf4;
  color: #166534;
  border-bottom: 1px solid #bbf7d0;
}

.test-summary.failure {
  background: #fef2f2;
  color: #dc2626;
  border-bottom: 1px solid #fecaca;
}

.test-steps {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.test-steps h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.test-step span:nth-child(2) {
  flex: 1;
  font-weight: 500;
  color: #374151;
}

.step-time {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.performance-summary {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.performance-summary h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.performance-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  font-size: 14px;
}

.stat span:first-child {
  color: #6b7280;
}

.stat span:last-child {
  font-family: monospace;
  font-weight: 500;
  color: #374151;
}

.test-errors {
  padding: 16px;
}

.test-errors h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #dc2626;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border-radius: 6px;
  border-left: 3px solid #dc2626;
}

.error-item span {
  font-size: 13px;
  color: #7f1d1d;
  line-height: 1.4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Accessibility Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #667eea;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10001;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* App Layout */
.app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  margin: 0;
  padding: 0;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.app-header h1 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.header-subtitle {
  margin: 0 0 2rem 0;
  font-size: 1.2rem;
  opacity: 0.95;
  line-height: 1.5;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.feature-highlights {
  margin-top: 2rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.feature-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* File Upload Styles */
.file-upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Hero Upload Section */
.hero-upload-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 2rem 0;
  padding: 2rem;
}

.upload-hero-content {
  text-align: center;
  margin-bottom: 2rem;
}

.upload-hero-content h2 {
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.upload-hero-content p {
  font-size: 1.1rem;
  color: #4a5568;
  margin-bottom: 0;
}

/* Quick Features */
.quick-features {
  margin: 2rem 0;
  text-align: center;
}

.quick-features .feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.quick-features .feature-item {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.quick-features .feature-item:hover {
  background: #edf2f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* How It Works Section */
.how-it-works-section {
  margin: 3rem 0;
  background: white;
  padding: 3rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.how-it-works-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  font-weight: 600;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.step-item {
  text-align: center;
  padding: 2rem 1rem;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem auto;
}

.step-item h3 {
  color: #2d3748;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.step-item p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

/* SEO Content Sections */
.seo-content-section {
  margin-bottom: 3rem;
  text-align: center;
}

.seo-content-section h2 {
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  font-weight: 600;
}

.seo-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.seo-step {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.seo-step h3 {
  color: #667eea;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.seo-step p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

.features-section {
  margin: 3rem 0;
  background: white;
  padding: 3rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.features-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  font-weight: 600;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-category h3 {
  color: #667eea;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.feature-category ul {
  list-style: none;
  padding: 0;
}

.feature-category li {
  padding: 0.5rem 0;
  color: #4a5568;
  position: relative;
  padding-left: 1.5rem;
}

.feature-category li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #48bb78;
  font-weight: bold;
}

.faq-section {
  margin: 3rem 0;
  background: #f7fafc;
  padding: 3rem 2rem;
  border-radius: 12px;
}

.faq-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  font-weight: 600;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: white;
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.faq-item h3 {
  background: #667eea;
  color: white;
  padding: 1rem 1.5rem;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.faq-item p {
  padding: 1.5rem;
  margin: 0;
  color: #4a5568;
  line-height: 1.6;
}

/* What is PDF Editing Section */
.what-is-pdf-editing {
  margin: 3rem 0;
  background: #f7fafc;
  padding: 3rem 2rem;
  border-radius: 12px;
}

.what-is-pdf-editing h2 {
  text-align: center;
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  font-weight: 600;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.content-block {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-block h3 {
  color: #667eea;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.content-block p {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.content-block ul {
  color: #4a5568;
  line-height: 1.6;
  padding-left: 1.5rem;
}

.content-block li {
  margin-bottom: 0.5rem;
}

/* Comparison Section */
.comparison-section {
  margin: 3rem 0;
  background: white;
  padding: 3rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.comparison-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  font-weight: 600;
}

.comparison-table {
  overflow-x: auto;
  margin-bottom: 3rem;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.comparison-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #2d3748;
}

.comparison-table td {
  color: #4a5568;
}

.comparison-table tr:last-child td {
  border-bottom: none;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.advantage-item {
  text-align: center;
  padding: 2rem 1rem;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.advantage-item h3 {
  color: #2d3748;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.advantage-item p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
  font-size: 0.9rem;
}

/* FAQ Categories */
.faq-category {
  margin-bottom: 3rem;
}

.faq-category h3 {
  color: #667eea;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}

.faq-category .faq-item h4 {
  background: #667eea;
  color: white;
  padding: 1rem 1.5rem;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.file-upload-area {
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  background-color: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: #667eea;
  background-color: #f7fafc;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  color: #667eea;
  margin-bottom: 1rem;
}

.file-upload-area h3 {
  margin: 0;
  font-size: 1.5rem;
  color: #2d3748;
}

.file-upload-area p {
  margin: 0;
  color: #718096;
  font-size: 1rem;
}

.file-input {
  display: none;
}

.file-input-label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #667eea;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.file-input-label:hover {
  background-color: #5a67d8;
}

.file-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.file-info p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #a0aec0;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #fed7d7;
  color: #c53030;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #feb2b2;
}

/* PDF Editor Styles */
.pdf-editor {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pdf-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-left h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #2d3748;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.back-button,
.save-button,
.download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.back-button {
  background-color: #e2e8f0;
  color: #4a5568;
}

.back-button:hover {
  background-color: #cbd5e0;
}

.save-button {
  background-color: #48bb78;
  color: white;
}

.save-button:hover {
  background-color: #38a169;
}

.download-button {
  background-color: #667eea;
  color: white;
}

.download-button:hover {
  background-color: #5a67d8;
}

.history-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #4a5568;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.history-button:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

.history-button.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.pdf-editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.toolbar {
  width: 250px;
  background-color: #f7fafc;
  border-right: 1px solid #e2e8f0;
  padding: 1rem;
  overflow-y: auto;
}

.pdf-viewer {
  flex: 1;
  padding: 1rem;
  overflow: auto;
  background-color: #f8fafc;
}

/* Loading and Error States */
.pdf-editor-loading,
.pdf-editor-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pdf-editor-loading p,
.pdf-editor-error p {
  color: #4a5568;
  font-size: 1.1rem;
  margin: 0;
}

/* PDF Viewer Styles */
.pdf-viewer-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
  margin-bottom: 1rem;
}

.page-controls,
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.control-button:hover:not(:disabled) {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info,
.zoom-info {
  font-weight: 500;
  color: #4a5568;
  min-width: 100px;
  text-align: center;
}

.pdf-document-container {
  flex: 1;
  overflow: auto;
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  justify-content: center;
}

.pdf-page-container {
  display: inline-block;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.pdf-viewer-loading,
.pdf-viewer-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #4a5568;
  background-color: #f7fafc;
  border-radius: 8px;
  margin: 1rem;
}

.pdf-page-loading,
.pdf-page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #4a5568;
  background-color: #f7fafc;
  border-radius: 8px;
  margin: 0.5rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #5a67d8;
}

/* Simple PDF Viewer Styles */
.simple-pdf-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.viewer-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.viewer-toggle-button {
  padding: 0.5rem 1rem;
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.viewer-toggle-button:hover {
  background-color: #5a67d8;
}

/* PDF Viewer Error Boundary Styles */
.pdf-viewer-error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background-color: #f7fafc;
  border-radius: 8px;
  margin: 1rem;
}

.pdf-viewer-error-boundary .error-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.pdf-viewer-error-boundary .error-icon {
  color: #f56565;
  margin-bottom: 1rem;
}

.pdf-viewer-error-boundary h3 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.pdf-viewer-error-boundary p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.pdf-viewer-error-boundary ul {
  text-align: left;
  color: #4a5568;
  margin-bottom: 1.5rem;
}

.pdf-viewer-error-boundary li {
  margin-bottom: 0.5rem;
}

.pdf-viewer-error-boundary .error-actions {
  margin-bottom: 1.5rem;
}

.pdf-viewer-error-boundary .error-details {
  text-align: left;
  background-color: #1a202c;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
}

.pdf-viewer-error-boundary .error-stack {
  font-size: 0.75rem;
  line-height: 1.4;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Enhanced Debug Information Styles */
.debug-details {
  margin-top: 1rem;
  background-color: #1a202c;
  border-radius: 6px;
  overflow: hidden;
}

.debug-details summary {
  padding: 0.75rem;
  background-color: #2d3748;
  color: #e2e8f0;
  cursor: pointer;
  font-weight: 500;
  border: none;
  outline: none;
}

.debug-details summary:hover {
  background-color: #4a5568;
}

.debug-info {
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  background-color: #1a202c;
}

.debug-log {
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: #a0aec0;
  padding: 0.25rem 0;
  border-bottom: 1px solid #2d3748;
}

.debug-log:last-child {
  border-bottom: none;
}

/* PDF Download Mode Styles */
.pdf-download-mode {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background-color: #f7fafc;
  border-radius: 8px;
  margin: 1rem;
}

.download-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.warning-icon {
  color: #ed8936;
  margin-bottom: 1rem;
}

.error-icon {
  color: #f56565;
  margin-bottom: 1rem;
}

.download-content h3 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.download-content p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.file-info {
  background-color: #edf2f7;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  text-align: left;
}

.file-info p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
}

.download-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1.5rem 0;
}

.download-button,
.open-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  text-decoration: none;
}

.download-button {
  background-color: #48bb78;
  color: white;
}

.download-button:hover {
  background-color: #38a169;
}

.open-button {
  background-color: #667eea;
  color: white;
}

.open-button:hover {
  background-color: #5a67d8;
}

/* Viewer Mode Controls */
.viewer-mode-controls {
  display: flex;
  gap: 0.5rem;
}

.viewer-mode-controls .control-button.active {
  background-color: #667eea;
  color: white;
}

.debug-panel {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f7fafc;
  border-radius: 6px;
}

/* Viewer Selector Styles */
.viewer-selector {
  display: flex;
  gap: 0.5rem;
  background-color: #f7fafc;
  padding: 0.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.viewer-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  background-color: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.viewer-button:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
}

.viewer-button.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

/* Canvas Container Styles */
.canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  background-color: #f7fafc;
  border-radius: 8px;
  overflow: auto;
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-info {
  font-size: 0.875rem;
  color: #4a5568;
  font-weight: 500;
  min-width: 100px;
  text-align: center;
}

/* Enhanced Canvas PDF Viewer Styles */
.pdf-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
}

.page-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-input {
  width: 60px;
  padding: 0.25rem 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  text-align: center;
  font-size: 0.875rem;
}

.page-total {
  font-size: 0.875rem;
  color: #4a5568;
  white-space: nowrap;
}

.zoom-select,
.quality-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.fit-controls,
.view-controls,
.transform-controls,
.quality-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.control-button.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.rendering-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Grid View Styles */
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;
}

.grid-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.grid-page:hover {
  border-color: #cbd5e0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.grid-page.active {
  border-color: #667eea;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.grid-canvas {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grid-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 200px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  color: #4a5568;
  font-size: 0.875rem;
}

.grid-page-number {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #4a5568;
  font-weight: 500;
}

/* Status Bar */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
  font-size: 0.75rem;
  color: #4a5568;
  border-radius: 0 0 8px 8px;
}

/* Canvas Container */
.canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background: #f7fafc;
  min-height: 400px;
  overflow: auto;
}

.pdf-canvas {
  transition: all 0.3s ease;
}

/* Responsive Design for Canvas Viewer */
@media (max-width: 768px) {
  .pdf-controls {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .navigation-controls,
  .zoom-controls,
  .fit-controls,
  .view-controls {
    width: 100%;
    justify-content: center;
  }

  .grid-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .canvas-container {
    padding: 1rem;
  }

  .status-bar {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }
}

/* Canvas Editing Overlay Styles */
.canvas-editing-overlay {
  position: relative;
  user-select: none;
}

.editing-tools {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.75rem;
  color: #4a5568;
}

.tool-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.tool-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.style-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.color-picker {
  width: 40px;
  height: 30px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
}

.stroke-width,
.font-size {
  width: 80px;
  height: 20px;
  cursor: pointer;
}

.text-input-modal {
  min-width: 200px;
}

.text-input-modal input {
  width: 100%;
  padding: 0.25rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.text-input-modal button {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.text-input-modal button:hover {
  background: #5a67d8;
}

.text-input-modal button:last-child {
  background: #e2e8f0;
  color: #4a5568;
}

.text-input-modal button:last-child:hover {
  background: #cbd5e0;
}

/* Annotation Styles */
.annotation-marker {
  position: absolute;
  background: rgba(102, 126, 234, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  z-index: 50;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.annotation-marker.comment {
  background: rgba(245, 101, 101, 0.8);
}

.annotation-marker.text {
  background: rgba(56, 178, 172, 0.8);
}

.annotation-marker:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

/* Mobile Responsive for Editing Overlay */
@media (max-width: 768px) {
  .editing-tools {
    flex-direction: row;
    top: auto;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    max-width: 90vw;
    overflow-x: auto;
  }

  .style-controls {
    flex-direction: row;
    top: auto;
    bottom: 60px;
    right: 50%;
    transform: translateX(50%);
  }

  .tool-btn {
    width: 35px;
    height: 35px;
    font-size: 0.7rem;
  }

  .stroke-width,
  .font-size {
    width: 60px;
  }
}

/* Progressive PDF Viewer Styles */
.progressive-pdf-viewer {
  width: 100%;
  height: 100%;
}

.viewer-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
}

.current-viewer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.viewer-name {
  font-weight: 600;
  color: #2d3748;
}

.viewer-description {
  color: #4a5568;
  font-size: 0.75rem;
}

.auto-progress-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 500;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.viewer-selection-bar {
  display: flex;
  background-color: #edf2f7;
  border-bottom: 1px solid #e2e8f0;
  overflow-x: auto;
}

.viewer-tab {
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
}

.viewer-tab:hover {
  background-color: #e2e8f0;
}

.viewer-tab.active {
  background-color: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.viewer-tab.success {
  color: #48bb78;
}

.viewer-tab.failed {
  color: #f56565;
}

.viewer-tab.testing {
  color: #ed8936;
}

.viewer-tab:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.active-viewer {
  flex: 1;
  height: calc(100vh - 200px);
  overflow: auto;
}

.viewer-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.viewer-button.success {
  background-color: #48bb78;
  color: white;
}

.viewer-button.failed {
  background-color: #f56565;
  color: white;
}

.viewer-button.testing {
  background-color: #ed8936;
  color: white;
}

.viewer-selection {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #edf2f7;
  border-radius: 6px;
}

.viewer-selection p {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2d3748;
}

/* Toolbar Styles */
.toolbar {
  background-color: #f7fafc;
  border-right: 1px solid #e2e8f0;
  padding: 1rem;
  overflow-y: auto;
  width: 280px;
}

.toolbar-section {
  margin-bottom: 2rem;
}

.toolbar-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.tool-buttons,
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button,
.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  color: #2d3748; /* Ensure dark text on white background */
  font-weight: 500;
}

.tool-button:hover,
.action-button:hover:not(:disabled) {
  background-color: #edf2f7;
  border-color: #cbd5e0;
}

.tool-button.active {
  background-color: #667eea;
  color: white !important; /* Ensure white text on blue background */
  border-color: #667eea;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.text-settings,
.highlight-settings,
.draw-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.text-settings label,
.highlight-settings label,
.draw-settings label {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.text-settings input,
.text-settings select,
.highlight-settings input,
.draw-settings input {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.875rem;
}

.draw-settings input[type="range"] {
  padding: 0;
}

/* Text Input Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.text-input-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  width: 320px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f7fafc;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background-color: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #718096;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: #e2e8f0;
  color: #4a5568;
}

.modal-content {
  padding: 1rem;
}

.text-input-section {
  margin-bottom: 1rem;
}

.text-input-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.text-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
}

.text-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.text-settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
}

.setting-group:last-child {
  grid-column: 1 / -1;
}

.setting-group label {
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #4a5568;
}

.setting-input,
.setting-select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.875rem;
}

.color-input {
  width: 100%;
  height: 40px;
  padding: 0.25rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
}

.text-preview {
  margin-bottom: 1rem;
}

.text-preview label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.preview-text {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: #f7fafc;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background-color: #f7fafc;
}

.cancel-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-button {
  background-color: #e2e8f0;
  color: #4a5568;
}

.cancel-button:hover {
  background-color: #cbd5e0;
}

.save-button {
  background-color: #667eea;
  color: white;
}

.save-button:hover {
  background-color: #5a67d8;
}

/* Comment Modal Styles */
.comment-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  width: 300px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
}

.comment-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f7fafc;
}

.header-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-icon h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

.comment-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.comment-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Highlight Tool Styles */
.highlight-tool {
  user-select: none;
}

.highlight-tool.active {
  cursor: crosshair;
}

.highlight-selection-overlay {
  border: 2px dashed #667eea;
  box-sizing: border-box;
}

/* Drawing Tool Styles */
.drawing-tool {
  user-select: none;
}

.drawing-tool.active {
  cursor: crosshair;
}

.drawing-svg {
  pointer-events: none;
}

/* Annotation Elements */
.annotation-element {
  position: absolute;
  pointer-events: auto;
  cursor: pointer;
}

.text-annotation {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.highlight-annotation {
  pointer-events: none;
}

.comment-annotation {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #fbbf24;
  border: 2px solid #f59e0b;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.comment-annotation:hover {
  background-color: #f59e0b;
  transform: scale(1.1);
}

.draw-annotation {
  pointer-events: none;
}

/* Tool States */
.pdf-viewer.text-mode {
  cursor: text;
}

.pdf-viewer.highlight-mode {
  cursor: crosshair;
}

.pdf-viewer.comment-mode {
  cursor: pointer;
}

.pdf-viewer.draw-mode {
  cursor: crosshair;
}

.pdf-viewer.erase-mode {
  cursor: not-allowed;
}

/* Image Insert Modal Styles */
.image-insert-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  z-index: 1000;
}

.image-upload-area {
  padding: 2rem;
  text-align: center;
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  margin: 1rem;
  background-color: #f7fafc;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  color: #4a5568;
}

.upload-label h4 {
  margin: 0;
  font-size: 1.25rem;
  color: #2d3748;
}

.upload-label p {
  margin: 0;
  font-size: 0.875rem;
}

.file-info {
  color: #a0aec0 !important;
  font-size: 0.75rem !important;
}

.image-preview-area {
  padding: 1rem;
}

.image-preview-container {
  position: relative;
  width: 100%;
  height: 300px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f7fafc;
  overflow: hidden;
  margin-bottom: 1rem;
}

.image-preview {
  position: absolute;
  border: 2px solid #667eea;
  border-radius: 4px;
  overflow: hidden;
  user-select: none;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  background-color: #667eea;
  border-radius: 50%;
  cursor: nw-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.image-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.position-inputs,
.size-inputs {
  display: flex;
  gap: 0.5rem;
}

.position-inputs input,
.size-inputs input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.875rem;
}

.rotate-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.rotate-button:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

.control-actions {
  grid-column: 1 / -1;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.reset-button,
.change-button {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.reset-button:hover,
.change-button:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

/* Status Bar Styles */
.status-bar {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 0.5rem 1rem;
  background-color: #f7fafc;
  border-top: 1px solid #e2e8f0;
  font-size: 0.875rem;
  color: #4a5568;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-label {
  font-weight: 500;
  color: #718096;
}

.status-value {
  color: #2d3748;
  font-weight: 500;
}

.status-tool {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-ready {
  margin-left: auto;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #48bb78;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced Toolbar Styles */
.toolbar-section:first-child {
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.tool-button {
  position: relative;
  overflow: hidden;
}

.tool-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tool-button:hover::before {
  left: 100%;
}

.tool-button.active {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Enhanced PDF Viewer Controls */
.pdf-controls {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.pdf-controls .control-button {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.pdf-controls .control-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.pdf-controls .page-info,
.pdf-controls .zoom-info {
  color: white;
  font-weight: 600;
}

/* Loading Animation Enhancement */
.loading-spinner {
  border-top-color: #667eea;
  animation: spin 1s linear infinite, pulse-border 2s ease-in-out infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-width: 4px;
  }
  50% {
    border-width: 6px;
  }
}

/* Enhanced Modal Animations */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.text-input-modal,
.comment-modal,
.image-insert-modal {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Save Modal Styles */
.save-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.save-summary {
  background-color: #f7fafc;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.save-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.file-name-input {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.file-name-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.checkbox-text {
  color: #4a5568;
}

.quality-slider {
  width: 100%;
  margin: 0.5rem 0;
}

.quality-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #718096;
}

.save-preview {
  background-color: #f7fafc;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1.5rem;
}

.save-preview h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #2d3748;
}

.preview-info p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #4a5568;
}

.save-modal .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background-color: #f7fafc;
}

.quick-save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #48bb78;
  background-color: #48bb78;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.quick-save-button:hover {
  background-color: #38a169;
  border-color: #38a169;
}

/* History Panel Styles */
.history-panel {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 300px;
  max-height: 80vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f7fafc;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.history-content {
  padding: 1rem;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.history-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.history-action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.history-action-button:hover:not(:disabled) {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

.history-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.history-list {
  margin-bottom: 1rem;
}

.history-section {
  margin-bottom: 1.5rem;
}

.history-section h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.25rem;
}

.history-empty {
  text-align: center;
  color: #a0aec0;
  font-size: 0.875rem;
  padding: 1rem;
  font-style: italic;
}

.history-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.history-item {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: white;
  transition: all 0.2s;
}

.history-item.current {
  border-color: #667eea;
  background-color: #f7fafc;
}

.history-item.redo-item {
  opacity: 0.7;
  border-style: dashed;
}

.history-item:hover {
  border-color: #cbd5e0;
  background-color: #f7fafc;
}

.history-item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
}

.history-description {
  font-size: 0.875rem;
  font-weight: 500;
  color: #2d3748;
  flex: 1;
}

.history-time {
  font-size: 0.75rem;
  color: #718096;
  white-space: nowrap;
  margin-left: 0.5rem;
}

.history-item-count {
  font-size: 0.75rem;
  color: #a0aec0;
}

.history-stats {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #718096;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

/* Error Boundary Styles */
.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7fafc;
  padding: 2rem;
}

.error-content {
  max-width: 600px;
  text-align: center;
  background-color: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.error-icon {
  color: #f56565;
  margin-bottom: 1.5rem;
}

.error-content h1 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  color: #2d3748;
}

.error-content p {
  margin: 0 0 2rem 0;
  color: #4a5568;
  line-height: 1.6;
}

.error-details {
  text-align: left;
  margin: 2rem 0;
  padding: 1rem;
  background-color: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.error-details summary {
  cursor: pointer;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 1rem;
}

.error-message,
.error-stack {
  margin: 1rem 0;
  font-size: 0.875rem;
}

.error-stack pre {
  background-color: #1a202c;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.75rem;
  line-height: 1.4;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.retry-button,
.reload-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.retry-button {
  background-color: #667eea;
  color: white;
}

.retry-button:hover {
  background-color: #5a67d8;
}

.reload-button {
  background-color: #e2e8f0;
  color: #4a5568;
}

.reload-button:hover {
  background-color: #cbd5e0;
}

/* Loading Overlay Styles */
.loading-overlay-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.2s ease-out;
}

.loading-overlay-content {
  background-color: white;
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.loading-spinner-large {
  color: #667eea;
  margin-bottom: 1.5rem;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.loading-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  color: #2d3748;
}

.loading-progress {
  margin-bottom: 1.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: #667eea;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  color: #4a5568;
  font-weight: 500;
}

.loading-cancel-button {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #4a5568;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.loading-cancel-button:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

/* Toast Styles */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.toast-visible {
  opacity: 1;
  transform: translateX(0);
}

.toast-removing {
  opacity: 0;
  transform: translateX(100%);
}

.toast-success {
  border-left-color: #48bb78;
}

.toast-error {
  border-left-color: #f56565;
}

.toast-warning {
  border-left-color: #ed8936;
}

.toast-info {
  border-left-color: #4299e1;
}

.toast-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.toast-success .toast-icon {
  color: #48bb78;
}

.toast-error .toast-icon {
  color: #f56565;
}

.toast-warning .toast-icon {
  color: #ed8936;
}

.toast-info .toast-icon {
  color: #4299e1;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.toast-message {
  font-size: 0.875rem;
  color: #4a5568;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.toast-action {
  font-size: 0.875rem;
  color: #667eea;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  text-decoration: underline;
  padding: 0;
}

.toast-action:hover {
  color: #5a67d8;
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  color: #a0aec0;
  padding: 0;
  margin-top: 0.125rem;
  transition: color 0.2s;
}

.toast-close:hover {
  color: #4a5568;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .toast-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }

  .error-boundary {
    padding: 1rem;
  }

  .error-content {
    padding: 2rem;
  }

  .loading-overlay-content {
    padding: 2rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }

  .app-header {
    padding: 2rem 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .header-subtitle {
    font-size: 1rem;
  }

  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .feature-item {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .seo-content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .seo-step {
    padding: 1.5rem;
  }

  .features-list {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .features-section {
    padding: 2rem 1rem;
  }

  .faq-section {
    padding: 2rem 1rem;
  }

  .pdf-editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }

  .pdf-editor-content {
    flex-direction: column;
  }

  .toolbar {
    width: 100%;
    max-height: 200px;
  }

  .pdf-controls {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.75rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .seo-step h3 {
    font-size: 1.1rem;
  }

  .features-section h2,
  .faq-section h2,
  .seo-content-section h2 {
    font-size: 1.5rem;
  }
}

/* Image PDF Editor Styles */
.image-pdf-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

.image-pdf-editor-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8fafc;
}

.loading-content {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.conversion-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  transition: width 0.3s ease;
}

.image-pdf-editor-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8fafc;
  text-align: center;
  padding: 2rem;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-left h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.conversion-method {
  font-size: 0.875rem;
  color: #718096;
  background: #edf2f7;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 1rem;
}

.tool-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.tool-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.tool-button:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.tool-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.zoom-button {
  padding: 0.5rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.zoom-button:hover {
  background: #edf2f7;
}

.zoom-level {
  font-weight: 600;
  color: #2d3748;
  min-width: 60px;
  text-align: center;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-button {
  padding: 0.5rem 1rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.page-button:hover:not(:disabled) {
  background: #edf2f7;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-weight: 600;
  color: #2d3748;
  min-width: 120px;
  text-align: center;
}

.editor-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  overflow: auto;
  background: #f1f5f9;
}

.pdf-canvas {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  background: white;
  max-width: 100%;
  max-height: 100%;
}

/* Responsive Design for Image Editor */
@media (max-width: 768px) {
  .editor-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .header-left,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .editor-toolbar {
    padding: 0.75rem 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .tool-group {
    justify-content: center;
    flex-wrap: wrap;
  }

  .zoom-controls,
  .page-controls {
    justify-content: center;
  }

  .editor-content {
    padding: 1rem;
  }

  .pdf-canvas {
    width: 100%;
    height: auto;
  }
}

/* Browser Compatibility Panel Styles */
.compatibility-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.compatibility-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.compatibility-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.browser-details {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.browser-details h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
}

.capability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.capability {
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

.capability.supported {
  background: #f0fff4;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.capability.not-supported {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.recommendations,
.browser-instructions,
.fallback-methods {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.recommendations:last-child,
.browser-instructions:last-child,
.fallback-methods:last-child {
  border-bottom: none;
}

.recommendations h4,
.browser-instructions h4,
.fallback-methods h4 {
  margin: 0 0 0.75rem 0;
  color: #2d3748;
  font-size: 0.95rem;
  font-weight: 600;
}

.recommendations ul,
.browser-instructions ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #4a5568;
  line-height: 1.5;
}

.recommendations li,
.browser-instructions li {
  margin-bottom: 0.5rem;
}

.fallback-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.fallback-button {
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  text-transform: capitalize;
}

.fallback-button:hover {
  background: #5a67d8;
}

.browser-info {
  font-size: 0.75rem;
  color: #4a5568;
  margin-left: 0.5rem;
}

.compatibility-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.compatibility-controls .control-button {
  position: relative;
}

.compatibility-controls .control-button svg:last-child {
  position: absolute;
  top: -2px;
  right: -2px;
  color: #f56565;
}
