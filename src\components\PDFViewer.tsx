import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw } from 'lucide-react'
import PDFWorkerManager from '../utils/pdfWorkerManager'

// Initialize worker manager
const workerManager = PDFWorkerManager.getInstance()

interface PDFViewerProps {
  file: File
  onPageClick?: (pageNumber: number, x: number, y: number) => void
}

const PDFViewer: React.FC<PDFViewerProps> = ({ file, onPageClick }) => {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.0)
  const [rotation, setRotation] = useState<number>(0)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [fileUrl, setFileUrl] = useState<string | null>(null)
  const [debugLogs, setDebugLogs] = useState<string[]>([])
  const [loadStartTime, setLoadStartTime] = useState<number>(0)
  const [documentLoadAttempts, setDocumentLoadAttempts] = useState<number>(0)

  // Enhanced debug logging
  const addDebugLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    const logMessage = `[${timestamp}] ${message}`
    console.log(`PDFViewer: ${logMessage}`)
    setDebugLogs(prev => [...prev.slice(-19), logMessage]) // Keep last 20 logs
  }, [])

  // Debug component state changes
  useEffect(() => {
    addDebugLog(`State change: loading=${isLoading}, error=${!!error}, fileUrl=${!!fileUrl}, numPages=${numPages}, attempts=${documentLoadAttempts}`)
  }, [isLoading, error, fileUrl, numPages, documentLoadAttempts, addDebugLog])

  // Create object URL for the file with enhanced debugging
  useEffect(() => {
    if (!file) return

    addDebugLog(`Initializing PDFViewer for file: ${file.name} (${file.size} bytes, ${file.type})`)
    setLoadStartTime(Date.now())
    setDocumentLoadAttempts(0)

    if (file.type !== 'application/pdf') {
      addDebugLog('Invalid file type detected')
      setError('Invalid file type. Please select a PDF file.')
      setIsLoading(false)
      return
    }

    // Create object URL
    let url: string | null = null
    try {
      addDebugLog('Creating object URL...')
      url = URL.createObjectURL(file)
      addDebugLog(`Object URL created successfully: ${url.substring(0, 50)}...`)

      // Test blob URL accessibility
      fetch(url, { method: 'HEAD' })
        .then(response => {
          addDebugLog(`Blob URL accessibility test: ${response.ok ? 'PASS' : 'FAIL'} (${response.status})`)
          if (response.ok) {
            setFileUrl(url)
            setIsLoading(true) // Keep loading until Document loads
            setError(null)
          } else {
            setError('PDF file cannot be accessed')
            setIsLoading(false)
          }
        })
        .catch(error => {
          addDebugLog(`Blob URL accessibility test failed: ${error.message}`)
          setError('Failed to access PDF file')
          setIsLoading(false)
        })

    } catch (error) {
      addDebugLog(`Error creating object URL: ${error}`)
      setError('Failed to process PDF file')
      setIsLoading(false)
      return
    }

    // Cleanup function
    return () => {
      if (url) {
        addDebugLog(`Cleaning up object URL: ${url.substring(0, 50)}...`)
        URL.revokeObjectURL(url)
      }
    }
  }, [file, addDebugLog])

  // Reset state when file changes
  useEffect(() => {
    setPageNumber(1)
    setNumPages(0)
    setScale(1.0)
    setRotation(0)
  }, [file])

  // Enhanced timeout with progressive fallback
  useEffect(() => {
    if (isLoading && fileUrl && documentLoadAttempts === 0) {
      addDebugLog('Setting up progressive timeout system')

      // First timeout at 15 seconds - try to reload
      const firstTimeout = setTimeout(() => {
        if (isLoading && documentLoadAttempts < 2) {
          addDebugLog('First timeout reached, attempting reload...')
          setDocumentLoadAttempts(prev => prev + 1)
          // Force re-render by updating fileUrl
          const currentUrl = fileUrl
          setFileUrl(null)
          setTimeout(() => setFileUrl(currentUrl), 100)
        }
      }, 15000)

      // Final timeout at 30 seconds - give up
      const finalTimeout = setTimeout(() => {
        if (isLoading) {
          addDebugLog('Final timeout reached, switching to fallback')
          setError('PDF loading timeout. The document may be too large or complex. Try using the Canvas or Simple viewer.')
          setIsLoading(false)
        }
      }, 30000)

      return () => {
        clearTimeout(firstTimeout)
        clearTimeout(finalTimeout)
      }
    }
  }, [isLoading, fileUrl, documentLoadAttempts, addDebugLog])

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    const loadTime = Date.now() - loadStartTime
    addDebugLog(`PDF loaded successfully: ${numPages} pages in ${loadTime}ms`)
    setNumPages(numPages)
    setIsLoading(false)
    setError(null)
  }, [addDebugLog, loadStartTime])

  const onDocumentLoadError = useCallback((error: any) => {
    const loadTime = Date.now() - loadStartTime
    const attempt = documentLoadAttempts + 1
    setDocumentLoadAttempts(attempt)

    addDebugLog(`PDF load error (attempt ${attempt}) after ${loadTime}ms: ${error?.message || error}`)

    let errorMessage = 'Failed to load PDF document'

    if (error?.message) {
      if (error.message.includes('Invalid PDF')) {
        errorMessage = 'Invalid PDF file format'
      } else if (error.message.includes('Password')) {
        errorMessage = 'Password-protected PDFs are not supported'
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error while loading PDF'
      } else if (error.message.includes('AbortError')) {
        errorMessage = 'PDF loading was cancelled'
      } else if (error.message.includes('worker')) {
        errorMessage = 'PDF.js worker failed to load'
      } else {
        errorMessage = `PDF Error: ${error.message}`
      }
    }

    // Retry logic for certain errors
    if (attempt < 3 && (error?.message?.includes('network') || error?.message?.includes('worker'))) {
      addDebugLog(`Retrying PDF load in 2 seconds (attempt ${attempt + 1}/3)`)
      setTimeout(() => {
        if (fileUrl) {
          addDebugLog('Retrying PDF load...')
          // Force re-render by updating a state
          setIsLoading(true)
        }
      }, 2000)
    } else {
      setError(errorMessage)
      setIsLoading(false)
    }
  }, [addDebugLog, loadStartTime, documentLoadAttempts, fileUrl])

  const onDocumentLoadProgress = useCallback(({ loaded, total }: { loaded: number; total: number }) => {
    const progress = Math.round((loaded / total) * 100)
    if (progress % 25 === 0) { // Log every 25%
      addDebugLog(`PDF loading progress: ${progress}% (${loaded}/${total} bytes)`)
    }
  }, [addDebugLog])

  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(1, prev - 1))
  }, [])

  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(numPages, prev + 1))
  }, [numPages])

  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(3.0, prev + 0.2))
  }, [])

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(0.5, prev - 0.2))
  }, [])

  const rotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360)
  }, [])

  const handlePageClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (onPageClick) {
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      onPageClick(pageNumber, x, y)
    }
  }, [onPageClick, pageNumber])

  // Memoize Document options with robust configuration
  const documentOptions = useMemo(() => ({
    cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
    cMapPacked: true,
    standardFontDataUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts/`,
    disableAutoFetch: false,
    disableStream: false,
    disableRange: false,
    verbosity: 1,
    // Additional options for better compatibility
    useSystemFonts: true,
    isEvalSupported: false,
    maxImageSize: 1024 * 1024, // 1MB max image size
    enableXfa: false, // Disable XFA forms for better compatibility
  }), [])

  if (!fileUrl) {
    return (
      <div className="pdf-viewer-loading">
        <div className="loading-spinner"></div>
        <p>Preparing PDF...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="pdf-viewer-error">
        <p>{error}</p>
        <button
          onClick={() => {
            setError(null)
            setIsLoading(true)
          }}
          className="retry-button"
        >
          Retry
        </button>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="pdf-viewer-loading">
        <div className="loading-spinner"></div>
        <p>Loading PDF... (Attempt {documentLoadAttempts + 1})</p>
        {process.env.NODE_ENV === 'development' && (
          <details className="debug-details">
            <summary>Debug Information ({debugLogs.length} logs)</summary>
            <div className="debug-info">
              {debugLogs.map((log, index) => (
                <div key={index} className="debug-log">{log}</div>
              ))}
            </div>
          </details>
        )}
      </div>
    )
  }

  return (
    <div className="pdf-viewer-container">
      <div className="pdf-controls">
        <div className="page-controls">
          <button 
            onClick={goToPrevPage} 
            disabled={pageNumber <= 1}
            className="control-button"
          >
            <ChevronLeft size={20} />
          </button>
          
          <span className="page-info">
            Page {pageNumber} of {numPages}
          </span>
          
          <button 
            onClick={goToNextPage} 
            disabled={pageNumber >= numPages}
            className="control-button"
          >
            <ChevronRight size={20} />
          </button>
        </div>

        <div className="zoom-controls">
          <button onClick={zoomOut} className="control-button">
            <ZoomOut size={20} />
          </button>
          
          <span className="zoom-info">
            {Math.round(scale * 100)}%
          </span>
          
          <button onClick={zoomIn} className="control-button">
            <ZoomIn size={20} />
          </button>
          
          <button onClick={rotate} className="control-button">
            <RotateCw size={20} />
          </button>
        </div>
      </div>

      <div className="pdf-document-container">
        <Document
          file={fileUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          onLoadProgress={onDocumentLoadProgress}
          loading={
            <div className="pdf-viewer-loading">
              <div className="loading-spinner"></div>
              <p>Loading PDF document...</p>
            </div>
          }
          error={
            <div className="pdf-viewer-error">
              <p>Failed to load PDF</p>
            </div>
          }
          noData={
            <div className="pdf-viewer-error">
              <p>No PDF file specified</p>
            </div>
          }
          options={documentOptions}
        >
          <div onClick={handlePageClick} className="pdf-page-container">
            <Page
              pageNumber={pageNumber}
              scale={scale}
              rotate={rotation}
              renderTextLayer={false}
              renderAnnotationLayer={false}
              loading={
                <div className="pdf-page-loading">
                  <div className="loading-spinner"></div>
                  <p>Loading page {pageNumber}...</p>
                </div>
              }
              error={
                <div className="pdf-page-error">
                  <p>Failed to load page {pageNumber}</p>
                </div>
              }
              noData={
                <div className="pdf-page-error">
                  <p>No page data</p>
                </div>
              }
            />
          </div>
        </Document>
      </div>
    </div>
  )
}

export default PDFViewer
