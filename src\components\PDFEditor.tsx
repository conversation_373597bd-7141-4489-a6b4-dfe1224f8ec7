import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON>Left, Download, Save, Clock } from 'lucide-react'
import PDFViewer from './PDFViewer'
import SimplePDFViewer from './SimplePDFViewer'
import CanvasPDFViewer from './CanvasPDFViewer'
// import ImagePDFEditor from './ImagePDFEditor'
import PDFViewerErrorBoundary from './PDFViewerErrorBoundary'
import Toolbar from './Toolbar'
import TextInputModal from './TextInputModal'
import CommentModal from './CommentModal'
import ImageInsertModal from './ImageInsertModal'
import StatusBar from './StatusBar'
import SaveModal from './SaveModal'
import HistoryPanel from './HistoryPanel'
import { PDFDocument } from 'pdf-lib'

// Define service classes locally to avoid import issues
class PDFEditingService {
  private elements: EditElement[] = []

  constructor(private pdfDoc: PDFDocument) {}

  async addTextElement(element: Omit<TextElement, 'id'>): Promise<string> {
    const id = Date.now().toString()
    this.elements.push({ ...element, id })
    return id
  }

  async addHighlightElement(element: Omit<HighlightElement, 'id'>): Promise<string> {
    const id = Date.now().toString()
    this.elements.push({ ...element, id })
    return id
  }

  async addCommentElement(element: Omit<CommentElement, 'id'>): Promise<string> {
    const id = Date.now().toString()
    this.elements.push({ ...element, id })
    return id
  }

  async addDrawElement(element: Omit<DrawElement, 'id'>): Promise<string> {
    const id = Date.now().toString()
    this.elements.push({ ...element, id })
    return id
  }

  async addImageElement(element: Omit<ImageElement, 'id'>): Promise<string> {
    const id = Date.now().toString()
    this.elements.push({ ...element, id })
    return id
  }

  getElements(): EditElement[] {
    return [...this.elements]
  }

  async generateModifiedPDF(): Promise<Uint8Array> {
    return await this.pdfDoc.save()
  }
}

class UndoRedoService {
  private history: EditElement[][] = []
  private redoStack: EditElement[][] = []
  private currentIndex = -1

  saveState(elements: EditElement[], description: string): void {
    this.history.push([...elements])
    this.redoStack = []
    this.currentIndex = this.history.length - 1
  }

  undo(): EditElement[] | null {
    if (this.currentIndex > 0) {
      this.redoStack.push(this.history[this.currentIndex])
      this.currentIndex--
      return [...this.history[this.currentIndex]]
    }
    return null
  }

  redo(): EditElement[] | null {
    if (this.redoStack.length > 0) {
      const state = this.redoStack.pop()!
      this.currentIndex++
      return [...state]
    }
    return null
  }

  canUndo(): boolean {
    return this.currentIndex > 0
  }

  canRedo(): boolean {
    return this.redoStack.length > 0
  }

  getHistory(): any[] {
    return this.history.map((state, index) => ({
      id: index.toString(),
      description: `State ${index}`,
      timestamp: Date.now(),
      elements: state
    }))
  }

  getRedoHistory(): any[] {
    return this.redoStack.map((state, index) => ({
      id: index.toString(),
      description: `Redo ${index}`,
      timestamp: Date.now(),
      elements: state
    }))
  }
}

// Define types locally to avoid import issues
type ToolType = 'select' | 'text' | 'highlight' | 'comment' | 'image' | 'draw' | 'erase'

interface TextElement {
  id: string
  x: number
  y: number
  text: string
  fontSize: number
  color: string
  fontFamily: string
  pageNumber: number
}

interface HighlightElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  color: string
  pageNumber: number
}

interface CommentElement {
  id: string
  x: number
  y: number
  text: string
  pageNumber: number
}

interface DrawElement {
  id: string
  points: { x: number; y: number }[]
  strokeWidth: number
  color: string
  pageNumber: number
}

interface ImageElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  imageData: string
  rotation: number
  pageNumber: number
}

type EditElement = TextElement | HighlightElement | CommentElement | DrawElement | ImageElement

interface SaveOptions {
  fileName: string
  includeOriginal: boolean
  compressImages: boolean
  quality: number
}

interface PDFEditorProps {
  file: File
  onBack: () => void
}

interface EditOperation {
  id: string
  type: 'text' | 'highlight' | 'comment' | 'image' | 'draw'
  data: any
  pageNumber: number
}

const PDFEditor: React.FC<PDFEditorProps> = ({ file, onBack }) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTool, setActiveTool] = useState<ToolType>('select')
  const [pdfDoc, setPdfDoc] = useState<PDFDocument | null>(null)
  const [editingService, setEditingService] = useState<PDFEditingService | null>(null)
  const [editOperations, setEditOperations] = useState<EditElement[]>([])
  const [undoRedoService] = useState(() => new UndoRedoService())
  const [textModalOpen, setTextModalOpen] = useState(false)
  const [textModalPosition, setTextModalPosition] = useState({ x: 0, y: 0, pageNumber: 1 })
  const [commentModalOpen, setCommentModalOpen] = useState(false)
  const [viewerType, setViewerType] = useState<'simple' | 'advanced' | 'canvas' | 'hybrid'>('simple') // Start with simple viewer as default
  const [commentModalPosition, setCommentModalPosition] = useState({ x: 0, y: 0, pageNumber: 1 })
  const [imageModalOpen, setImageModalOpen] = useState(false)
  const [imageModalPosition, setImageModalPosition] = useState({ x: 0, y: 0, pageNumber: 1 })
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [zoomLevel, setZoomLevel] = useState(1.0)
  const [saveModalOpen, setSaveModalOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [historyPanelOpen, setHistoryPanelOpen] = useState(false)

  useEffect(() => {
    // Initialize PDF loading
    const loadPDF = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const arrayBuffer = await file.arrayBuffer()
        const pdfDocument = await PDFDocument.load(arrayBuffer)
        setPdfDoc(pdfDocument)

        const service = new PDFEditingService(pdfDocument)
        setEditingService(service)

        setIsLoading(false)
      } catch (err) {
        setError('Failed to load PDF file')
        setIsLoading(false)
        console.error('PDF loading error:', err)
      }
    }

    loadPDF()
  }, [file])

  // No auto-fallback needed since we start with simple viewer

  const handlePageClick = useCallback((pageNumber: number, x: number, y: number) => {
    if (activeTool === 'text') {
      setTextModalPosition({ x, y, pageNumber })
      setTextModalOpen(true)
    } else if (activeTool === 'comment') {
      setCommentModalPosition({ x, y, pageNumber })
      setCommentModalOpen(true)
    } else if (activeTool === 'image') {
      setImageModalPosition({ x, y, pageNumber })
      setImageModalOpen(true)
    }
  }, [activeTool])

  const handleTextSave = useCallback(async (text: string, fontSize: number, color: string, fontFamily: string) => {
    if (!editingService) return

    try {
      const elementId = await editingService.addTextElement({
        x: textModalPosition.x,
        y: textModalPosition.y,
        text,
        fontSize,
        color,
        fontFamily,
        pageNumber: textModalPosition.pageNumber
      })

      const newElements = editingService.getElements()
      undoRedoService.saveState(editOperations, 'Add text')
      setEditOperations(newElements)
      setTextModalOpen(false)
    } catch (err) {
      console.error('Error adding text:', err)
      setError('Failed to add text')
    }
  }, [editingService, textModalPosition, editOperations])

  const handleTextCancel = useCallback(() => {
    setTextModalOpen(false)
  }, [])

  const handleCommentSave = useCallback(async (text: string) => {
    if (!editingService) return

    try {
      await editingService.addCommentElement({
        x: commentModalPosition.x,
        y: commentModalPosition.y,
        text,
        pageNumber: commentModalPosition.pageNumber
      })

      const newElements = editingService.getElements()
      undoRedoService.saveState(editOperations, 'Add comment')
      setEditOperations(newElements)
      setCommentModalOpen(false)
    } catch (err) {
      console.error('Error adding comment:', err)
      setError('Failed to add comment')
    }
  }, [editingService, commentModalPosition, editOperations])

  const handleCommentCancel = useCallback(() => {
    setCommentModalOpen(false)
  }, [])

  const handleHighlight = useCallback(async (x: number, y: number, width: number, height: number, pageNumber: number) => {
    if (!editingService) return

    try {
      await editingService.addHighlightElement({
        x,
        y,
        width,
        height,
        color: '#ffff00', // Default yellow highlight
        pageNumber
      })

      const newElements = editingService.getElements()
      undoRedoService.saveState(editOperations, 'Add highlight')
      setEditOperations(newElements)
    } catch (err) {
      console.error('Error adding highlight:', err)
      setError('Failed to add highlight')
    }
  }, [editingService, editOperations])

  const handleDrawComplete = useCallback(async (points: { x: number; y: number }[], pageNumber: number) => {
    if (!editingService) return

    try {
      await editingService.addDrawElement({
        points,
        strokeWidth: 2, // Default stroke width
        color: '#000000', // Default black color
        pageNumber
      })

      const newElements = editingService.getElements()
      undoRedoService.saveState(editOperations, 'Add drawing')
      setEditOperations(newElements)
    } catch (err) {
      console.error('Error adding drawing:', err)
      setError('Failed to add drawing')
    }
  }, [editingService, editOperations])

  const handleImageSave = useCallback(async (
    imageData: string,
    x: number,
    y: number,
    width: number,
    height: number,
    rotation: number
  ) => {
    if (!editingService) return

    try {
      await editingService.addImageElement({
        x,
        y,
        width,
        height,
        imageData,
        rotation,
        pageNumber: imageModalPosition.pageNumber
      })

      const newElements = editingService.getElements()
      undoRedoService.saveState(editOperations, 'Add image')
      setEditOperations(newElements)
      setImageModalOpen(false)
    } catch (err) {
      console.error('Error adding image:', err)
      setError('Failed to add image')
    }
  }, [editingService, imageModalPosition, editOperations])

  const handleImageCancel = useCallback(() => {
    setImageModalOpen(false)
  }, [])

  const handleUndo = useCallback(() => {
    const previousState = undoRedoService.undo()
    if (previousState !== null) {
      setEditOperations(previousState)
    }
  }, [undoRedoService])

  const handleRedo = useCallback(() => {
    const nextState = undoRedoService.redo()
    if (nextState !== null) {
      setEditOperations(nextState)
    }
  }, [undoRedoService])

  const handleSave = useCallback(() => {
    setSaveModalOpen(true)
  }, [])

  const handleSaveWithOptions = useCallback(async (options: SaveOptions) => {
    if (!editingService) return

    try {
      setIsSaving(true)
      setSaveModalOpen(false)

      // Generate modified PDF with all edits applied
      const modifiedPdfBytes = await editingService.generateModifiedPDF()

      // Create blob and download
      const blob = new Blob([modifiedPdfBytes], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = options.fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      setIsSaving(false)
    } catch (err) {
      console.error('Save error:', err)
      setError('Failed to save PDF')
      setIsSaving(false)
    }
  }, [editingService])

  const handleSaveCancel = useCallback(() => {
    setSaveModalOpen(false)
  }, [])

  const handleDownload = () => {
    handleSave()
  }

  if (isLoading) {
    return (
      <div className="pdf-editor-loading">
        <div className="loading-spinner"></div>
        <p>Loading PDF...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="pdf-editor-error">
        <p>{error}</p>
        <button onClick={onBack} className="back-button">
          <ArrowLeft size={20} />
          Back to Upload
        </button>
      </div>
    )
  }

  return (
    <div className="pdf-editor">
      <div className="pdf-editor-header">
        <div className="header-left">
          <button onClick={onBack} className="back-button">
            <ArrowLeft size={20} />
            Back
          </button>
          <h2>{file.name}</h2>
        </div>
        
        <div className="header-right">
          <button
            onClick={() => setHistoryPanelOpen(!historyPanelOpen)}
            className={`history-button ${historyPanelOpen ? 'active' : ''}`}
          >
            <Clock size={20} />
            History
          </button>
          <button onClick={handleSave} className="save-button">
            <Save size={20} />
            Save
          </button>
          <button onClick={handleDownload} className="download-button">
            <Download size={20} />
            Download
          </button>
        </div>
      </div>
      
      <div className="pdf-editor-content">
        <Toolbar
          activeTool={activeTool}
          onToolChange={setActiveTool}
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={undoRedoService.canUndo()}
          canRedo={undoRedoService.canRedo()}
        />

        <div className="pdf-viewer">
          <PDFViewerErrorBoundary
            onError={(error) => {
              console.error('PDF Viewer crashed, switching to next viewer:', error)
              if (viewerType === 'advanced') {
                setViewerType('canvas')
              } else if (viewerType === 'canvas') {
                setViewerType('simple')
              }
            }}
          >
            {viewerType === 'simple' && (
              <SimplePDFViewer
                file={file}
                onPageClick={handlePageClick}
              />
            )}
            {viewerType === 'advanced' && (
              <PDFViewer
                file={file}
                onPageClick={handlePageClick}
              />
            )}
            {viewerType === 'canvas' && (
              <CanvasPDFViewer
                file={file}
                onPageClick={handlePageClick}
              />
            )}
          </PDFViewerErrorBoundary>

          {/* Hybrid Image-Based Editor */}
          {viewerType === 'hybrid' && (
            <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h3>Hybrid Editor (Coming Soon)</h3>
              <p>The hybrid PDF editor is being prepared...</p>
              <button onClick={() => setViewerType('simple')} style={{ padding: '0.5rem 1rem', marginTop: '1rem' }}>
                Use Simple Viewer Instead
              </button>
            </div>
          )}

          {/* Viewer Selection Controls - Only show if not using hybrid */}
          {viewerType !== 'hybrid' && (
            <div className="viewer-controls">
              <div className="viewer-selector">
                <button
                  onClick={() => setViewerType('hybrid')}
                  className={`viewer-button ${viewerType === 'hybrid' ? 'active' : ''}`}
                >
                  🎯 Hybrid Editor
                </button>
                <button
                  onClick={() => setViewerType('simple')}
                  className={`viewer-button ${viewerType === 'simple' ? 'active' : ''}`}
                >
                  Reliable
                </button>
                <button
                  onClick={() => setViewerType('canvas')}
                  className={`viewer-button ${viewerType === 'canvas' ? 'active' : ''}`}
                >
                  Canvas
                </button>
                <button
                  onClick={() => setViewerType('advanced')}
                  className={`viewer-button ${viewerType === 'advanced' ? 'active' : ''}`}
                >
                  Advanced
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <StatusBar
        activeTool={activeTool}
        currentPage={currentPage}
        totalPages={totalPages}
        zoomLevel={zoomLevel}
        editCount={editOperations.length}
        fileName={file.name}
      />

      <TextInputModal
        isOpen={textModalOpen}
        x={textModalPosition.x}
        y={textModalPosition.y}
        onSave={handleTextSave}
        onCancel={handleTextCancel}
      />

      <CommentModal
        isOpen={commentModalOpen}
        x={commentModalPosition.x}
        y={commentModalPosition.y}
        onSave={handleCommentSave}
        onCancel={handleCommentCancel}
      />

      <ImageInsertModal
        isOpen={imageModalOpen}
        x={imageModalPosition.x}
        y={imageModalPosition.y}
        onSave={handleImageSave}
        onCancel={handleImageCancel}
      />

      <SaveModal
        isOpen={saveModalOpen}
        fileName={file.name}
        editCount={editOperations.length}
        onSave={handleSaveWithOptions}
        onCancel={handleSaveCancel}
      />

      <HistoryPanel
        isOpen={historyPanelOpen}
        undoHistory={undoRedoService.getHistory()}
        redoHistory={undoRedoService.getRedoHistory()}
        onUndo={handleUndo}
        onRedo={handleRedo}
        canUndo={undoRedoService.canUndo()}
        canRedo={undoRedoService.canRedo()}
        onClose={() => setHistoryPanelOpen(false)}
      />
    </div>
  )
}

export default PDFEditor
