import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON>Left, Download, Save, Clock } from 'lucide-react'
import CanvasPDFViewer from './CanvasPDFViewer'
import Toolbar from './Toolbar'
import TextInputModal from './TextInputModal'
import CommentModal from './CommentModal'
import ImageInsertModal from './ImageInsertModal'
import StatusBar from './StatusBar'
import SaveModal from './SaveModal'
import HistoryPanel from './HistoryPanel'
import { CanvasPDFService, type EditElement } from '../services/canvasPDFService'
import { UndoRedoService } from '../services/undoRedoService'

// Define types locally to avoid import issues
type ToolType = 'select' | 'text' | 'highlight' | 'comment' | 'image' | 'draw' | 'erase'

interface SaveOptions {
  fileName: string
  includeOriginal: boolean
  compressImages: boolean
  quality: number
}

interface PDFEditorProps {
  file: File
  onBack: () => void
}

const PDFEditor: React.FC<PDFEditorProps> = ({ file, onBack }) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTool, setActiveTool] = useState<ToolType>('select')
  const [canvasPDFService, setCanvasPDFService] = useState<CanvasPDFService | null>(null)
  const [editOperations, setEditOperations] = useState<EditElement[]>([])
  const [undoRedoService] = useState(() => new UndoRedoService())
  const [textModalOpen, setTextModalOpen] = useState(false)
  const [textModalPosition, setTextModalPosition] = useState({ x: 0, y: 0, pageNumber: 1 })
  const [commentModalOpen, setCommentModalOpen] = useState(false)
  const [commentModalPosition, setCommentModalPosition] = useState({ x: 0, y: 0, pageNumber: 1 })
  const [imageModalOpen, setImageModalOpen] = useState(false)
  const [imageModalPosition, setImageModalPosition] = useState({ x: 0, y: 0, pageNumber: 1 })
  const [currentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [zoomLevel] = useState(1.0)
  const [saveModalOpen, setSaveModalOpen] = useState(false)
  const [, setIsSaving] = useState(false)
  const [historyPanelOpen, setHistoryPanelOpen] = useState(false)

  useEffect(() => {
    // Initialize Canvas PDF Service
    const loadPDF = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const service = new CanvasPDFService()
        const result = await service.loadPDF(file)

        if (result.success) {
          setCanvasPDFService(service)
          setTotalPages(result.numPages)
          setIsLoading(false)
        } else {
          setError('Failed to load PDF file')
          setIsLoading(false)
        }
      } catch (err) {
        setError('Failed to load PDF file')
        setIsLoading(false)
        console.error('PDF loading error:', err)
      }
    }

    loadPDF()
  }, [file])

  // No auto-fallback needed since we start with simple viewer

  const handlePageClick = useCallback((pageNumber: number, x: number, y: number) => {
    if (activeTool === 'text') {
      setTextModalPosition({ x, y, pageNumber })
      setTextModalOpen(true)
    } else if (activeTool === 'comment') {
      setCommentModalPosition({ x, y, pageNumber })
      setCommentModalOpen(true)
    } else if (activeTool === 'image') {
      setImageModalPosition({ x, y, pageNumber })
      setImageModalOpen(true)
    }
  }, [activeTool])

  const handleTextSave = useCallback(async (text: string, fontSize: number, color: string, fontFamily: string) => {
    if (!canvasPDFService) return

    try {
      await canvasPDFService.addTextElement({
        x: textModalPosition.x,
        y: textModalPosition.y,
        text,
        fontSize,
        color,
        fontFamily,
        pageNumber: textModalPosition.pageNumber
      })

      const newElements = canvasPDFService.getEditElements()
      undoRedoService.saveState(editOperations, 'Add text')
      setEditOperations(newElements)
      setTextModalOpen(false)
    } catch (err) {
      console.error('Error adding text:', err)
      setError('Failed to add text')
    }
  }, [canvasPDFService, textModalPosition, editOperations, undoRedoService])

  const handleTextCancel = useCallback(() => {
    setTextModalOpen(false)
  }, [])

  const handleCommentSave = useCallback(async (text: string) => {
    if (!canvasPDFService) return

    try {
      await canvasPDFService.addCommentElement({
        x: commentModalPosition.x,
        y: commentModalPosition.y,
        text,
        pageNumber: commentModalPosition.pageNumber
      })

      const newElements = canvasPDFService.getEditElements()
      undoRedoService.saveState(editOperations, 'Add comment')
      setEditOperations(newElements)
      setCommentModalOpen(false)
    } catch (err) {
      console.error('Error adding comment:', err)
      setError('Failed to add comment')
    }
  }, [canvasPDFService, commentModalPosition, editOperations, undoRedoService])

  const handleCommentCancel = useCallback(() => {
    setCommentModalOpen(false)
  }, [])



  const handleImageSave = useCallback(async (
    imageData: string,
    x: number,
    y: number,
    width: number,
    height: number,
    rotation: number
  ) => {
    if (!canvasPDFService) return

    try {
      await canvasPDFService.addImageElement({
        x,
        y,
        width,
        height,
        imageData,
        rotation,
        pageNumber: imageModalPosition.pageNumber
      })

      const newElements = canvasPDFService.getEditElements()
      undoRedoService.saveState(editOperations, 'Add image')
      setEditOperations(newElements)
      setImageModalOpen(false)
    } catch (err) {
      console.error('Error adding image:', err)
      setError('Failed to add image')
    }
  }, [canvasPDFService, imageModalPosition, editOperations, undoRedoService])

  const handleImageCancel = useCallback(() => {
    setImageModalOpen(false)
  }, [])

  const handleUndo = useCallback(() => {
    const previousState = undoRedoService.undo()
    if (previousState !== null) {
      setEditOperations(previousState)
    }
  }, [undoRedoService])

  const handleRedo = useCallback(() => {
    const nextState = undoRedoService.redo()
    if (nextState !== null) {
      setEditOperations(nextState)
    }
  }, [undoRedoService])

  const handleSave = useCallback(() => {
    setSaveModalOpen(true)
  }, [])

  const handleSaveWithOptions = useCallback(async (options: SaveOptions) => {
    if (!canvasPDFService) return

    try {
      setIsSaving(true)
      setSaveModalOpen(false)

      // Generate modified PDF with all canvas edits applied
      const modifiedPdfBytes = await canvasPDFService.generateModifiedPDF()

      // Create blob and download
      const blob = new Blob([modifiedPdfBytes], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = options.fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      setIsSaving(false)
    } catch (err) {
      console.error('Save error:', err)
      setError('Failed to save PDF')
      setIsSaving(false)
    }
  }, [canvasPDFService])

  const handleSaveCancel = useCallback(() => {
    setSaveModalOpen(false)
  }, [])

  const handleDownload = () => {
    handleSave()
  }

  if (isLoading) {
    return (
      <div className="pdf-editor-loading">
        <div className="loading-spinner"></div>
        <p>Loading PDF...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="pdf-editor-error">
        <p>{error}</p>
        <button onClick={onBack} className="back-button">
          <ArrowLeft size={20} />
          Back to Upload
        </button>
      </div>
    )
  }

  return (
    <div className="pdf-editor">
      <div className="pdf-editor-header">
        <div className="header-left">
          <button onClick={onBack} className="back-button">
            <ArrowLeft size={20} />
            Back
          </button>
          <h2>{file.name}</h2>
        </div>
        
        <div className="header-right">
          <button
            onClick={() => setHistoryPanelOpen(!historyPanelOpen)}
            className={`history-button ${historyPanelOpen ? 'active' : ''}`}
          >
            <Clock size={20} />
            History
          </button>
          <button onClick={handleSave} className="save-button">
            <Save size={20} />
            Save
          </button>
          <button onClick={handleDownload} className="download-button">
            <Download size={20} />
            Download
          </button>
        </div>
      </div>
      
      <div className="pdf-editor-content">
        <Toolbar
          activeTool={activeTool}
          onToolChange={setActiveTool}
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={undoRedoService.canUndo()}
          canRedo={undoRedoService.canRedo()}
        />

        <div className="pdf-viewer">
          <CanvasPDFViewer
            file={file}
            onPageClick={handlePageClick}
          />
        </div>
      </div>

      <StatusBar
        activeTool={activeTool}
        currentPage={currentPage}
        totalPages={totalPages}
        zoomLevel={zoomLevel}
        editCount={editOperations.length}
        fileName={file.name}
      />

      <TextInputModal
        isOpen={textModalOpen}
        x={textModalPosition.x}
        y={textModalPosition.y}
        onSave={handleTextSave}
        onCancel={handleTextCancel}
      />

      <CommentModal
        isOpen={commentModalOpen}
        x={commentModalPosition.x}
        y={commentModalPosition.y}
        onSave={handleCommentSave}
        onCancel={handleCommentCancel}
      />

      <ImageInsertModal
        isOpen={imageModalOpen}
        x={imageModalPosition.x}
        y={imageModalPosition.y}
        onSave={handleImageSave}
        onCancel={handleImageCancel}
      />

      <SaveModal
        isOpen={saveModalOpen}
        fileName={file.name}
        editCount={editOperations.length}
        onSave={handleSaveWithOptions}
        onCancel={handleSaveCancel}
      />

      <HistoryPanel
        isOpen={historyPanelOpen}
        undoHistory={undoRedoService.getHistory()}
        redoHistory={undoRedoService.getRedoHistory()}
        onUndo={handleUndo}
        onRedo={handleRedo}
        canUndo={undoRedoService.canUndo()}
        canRedo={undoRedoService.canRedo()}
        onClose={() => setHistoryPanelOpen(false)}
      />
    </div>
  )
}

export default PDFEditor
