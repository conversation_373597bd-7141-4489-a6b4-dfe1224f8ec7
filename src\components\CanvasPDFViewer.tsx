import React, { useState, useEffect, useCallback, useRef } from 'react'
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Download, AlertTriangle, RotateCw, Maximize2, Grid3X3, Edit } from 'lucide-react'
import { CanvasPDFService, type CanvasPageData, type CanvasAnnotation } from '../services/canvasPDFService'
import CanvasEditingOverlay from './CanvasEditingOverlay'

interface RenderQuality {
  scale: number
  name: string
  description: string
}

interface CanvasPDFViewerProps {
  file: File
  onPageClick?: (pageNumber: number, x: number, y: number) => void
}

const CanvasPDFViewer: React.FC<CanvasPDFViewerProps> = ({ file, onPageClick }) => {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.0)
  const [rotation, setRotation] = useState<number>(0) // Used in rotateClockwise function
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [debugLogs, setDebugLogs] = useState<string[]>([])
  const [canvasPDFService, setCanvasPDFService] = useState<CanvasPDFService | null>(null)
  const [pageCache, setPageCache] = useState<Map<number, CanvasPageData>>(new Map()) // Used in rotateClockwise function
  const [renderQuality, setRenderQuality] = useState<RenderQuality>({ scale: 2.0, name: 'High', description: 'High quality rendering' })
  const [isRendering, setIsRendering] = useState<boolean>(false)
  const [viewMode, setViewMode] = useState<'single' | 'continuous' | 'grid'>('single')
  const [fitMode, setFitMode] = useState<'width' | 'height' | 'page' | 'custom'>('page')
  const [isEditingMode, setIsEditingMode] = useState<boolean>(false)
  const [annotations, setAnnotations] = useState<CanvasAnnotation[]>([])
  const [canvasDisplaySize, setCanvasDisplaySize] = useState({ width: 0, height: 0 })

  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Available render qualities
  const qualityOptions: RenderQuality[] = [
    { scale: 1.0, name: 'Draft', description: 'Fast rendering, lower quality' },
    { scale: 1.5, name: 'Standard', description: 'Balanced quality and performance' },
    { scale: 2.0, name: 'High', description: 'High quality rendering' },
    { scale: 3.0, name: 'Ultra', description: 'Maximum quality, slower rendering' }
  ]

  // Debug logging
  const addDebugLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    const logMessage = `[${timestamp}] ${message}`
    console.log(`CanvasPDFViewer: ${logMessage}`)
    setDebugLogs(prev => [...prev.slice(-9), logMessage])
  }, [])

  // Initialize Canvas PDF Service and load PDF
  useEffect(() => {
    if (!file) return

    const initializeService = async () => {
      try {
        addDebugLog(`Loading PDF: ${file.name} (${file.size} bytes)`)
        setIsLoading(true)
        setError(null)

        const service = new CanvasPDFService()
        const result = await service.loadPDF(file)

        if (result.success) {
          setCanvasPDFService(service)
          setNumPages(result.numPages)
          service.setRenderScale(renderQuality.scale)
          addDebugLog(`PDF loaded successfully: ${result.numPages} pages`)
          setIsLoading(false)
        } else {
          setError('Failed to load PDF file')
          setIsLoading(false)
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        addDebugLog(`PDF loading failed: ${errorMessage}`)
        setError(`Failed to load PDF: ${errorMessage}`)
        setIsLoading(false)
      }
    }

    initializeService()
  }, [file, addDebugLog, renderQuality.scale])

  // Render page using Canvas PDF Service
  const renderPageToCanvas = useCallback(async (pageNum: number): Promise<CanvasPageData | null> => {
    if (!canvasPDFService) return null

    try {
      addDebugLog(`Rendering page ${pageNum}`)
      setIsRendering(true)

      const pageData = await canvasPDFService.renderPageToCanvas(pageNum)
      if (pageData) {
        addDebugLog(`Page ${pageNum} rendered successfully`)
        return pageData
      } else {
        addDebugLog(`Failed to render page ${pageNum}`)
        return null
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      addDebugLog(`Page ${pageNum} rendering failed: ${errorMessage}`)
      setError(`Failed to render page ${pageNum}: ${errorMessage}`)
      return null
    } finally {
      setIsRendering(false)
    }
  }, [canvasPDFService, addDebugLog])

  // Render current page to display canvas
  const renderCurrentPage = useCallback(async () => {
    if (!canvasRef.current) return

    const pageData = await renderPageToCanvas(pageNumber)
    if (!pageData) return

    const displayCanvas = canvasRef.current
    const displayContext = displayCanvas.getContext('2d')

    if (!displayContext) return

    // Calculate display scale based on fit mode
    let displayScale = scale
    if (fitMode !== 'custom' && containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect()

      switch (fitMode) {
        case 'width':
          displayScale = (containerRect.width - 40) / pageData.originalWidth
          break
        case 'height':
          displayScale = (containerRect.height - 40) / pageData.originalHeight
          break
        case 'page': {
          const widthScale = (containerRect.width - 40) / pageData.originalWidth
          const heightScale = (containerRect.height - 40) / pageData.originalHeight
          displayScale = Math.min(widthScale, heightScale)
          break
        }
      }
    }

    // Set display canvas size
    displayCanvas.width = pageData.originalWidth * displayScale
    displayCanvas.height = pageData.originalHeight * displayScale

    // Clear and draw
    displayContext.clearRect(0, 0, displayCanvas.width, displayCanvas.height)
    displayContext.imageSmoothingEnabled = true
    displayContext.imageSmoothingQuality = 'high'

    displayContext.drawImage(
      pageData.canvas,
      0, 0, pageData.originalWidth, pageData.originalHeight,
      0, 0, displayCanvas.width, displayCanvas.height
    )

    addDebugLog(`Page ${pageNumber} displayed at scale ${displayScale.toFixed(2)}`)
  }, [pageNumber, scale, fitMode, renderPageToCanvas, addDebugLog])

  // Preload adjacent pages for smooth navigation
  const preloadAdjacentPages = useCallback(async () => {
    if (!canvasPDFService || isRendering) return

    const pagesToPreload = []

    // Preload previous and next pages
    if (pageNumber > 1) pagesToPreload.push(pageNumber - 1)
    if (pageNumber < numPages) pagesToPreload.push(pageNumber + 1)

    // In grid mode, preload more pages
    if (viewMode === 'grid') {
      for (let i = Math.max(1, pageNumber - 2); i <= Math.min(numPages, pageNumber + 2); i++) {
        if (i !== pageNumber) pagesToPreload.push(i)
      }
    }

    // Only preload pages not in cache
    const uncachedPages = pagesToPreload.filter(page => {
      const cached = canvasPDFService.getCachedPage(page)
      return !cached || !cached.rendered
    })

    if (uncachedPages.length > 0) {
      addDebugLog(`Preloading pages: ${uncachedPages.join(', ')}`)

      // Render pages in background
      for (const page of uncachedPages) {
        renderPageToCanvas(page)
      }
    }
  }, [canvasPDFService, pageNumber, numPages, viewMode, isRendering, addDebugLog, renderPageToCanvas])

  // Batch render multiple pages for grid view
  const renderPageGrid = useCallback(async () => {
    if (!containerRef.current || viewMode !== 'grid') return

    const startPage = Math.max(1, pageNumber - 2)
    const endPage = Math.min(numPages, pageNumber + 2)
    const pagesToRender = []

    for (let i = startPage; i <= endPage; i++) {
      pagesToRender.push(i)
    }

    addDebugLog(`Rendering grid view for pages ${startPage}-${endPage}`)

    // Render all pages in parallel
    const renderPromises = pagesToRender.map(page => renderPageToCanvas(page))
    await Promise.all(renderPromises)

    addDebugLog(`Grid view rendering complete`)
  }, [pageNumber, numPages, viewMode, renderPageToCanvas, addDebugLog])

  // Main render effect
  useEffect(() => {
    if (canvasPDFService && !isLoading) {
      if (viewMode === 'grid') {
        renderPageGrid()
      } else {
        renderCurrentPage().then(() => {
          // Preload adjacent pages after current page is rendered
          setTimeout(preloadAdjacentPages, 100)
          // Optimize memory usage
          setTimeout(() => canvasPDFService.optimizeMemory(5), 1000)
        })
      }
    }
  }, [canvasPDFService, pageNumber, scale, isLoading, viewMode, fitMode, renderQuality, renderCurrentPage, renderPageGrid, preloadAdjacentPages])

  // Advanced navigation functions
  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => {
      const newPage = Math.max(1, prev - 1)
      addDebugLog(`Navigating to page ${newPage}`)
      return newPage
    })
  }, [addDebugLog])

  const goToNextPage = useCallback(() => {
    setPageNumber(prev => {
      const newPage = Math.min(numPages, prev + 1)
      addDebugLog(`Navigating to page ${newPage}`)
      return newPage
    })
  }, [numPages, addDebugLog])

  const goToPage = useCallback((page: number) => {
    const targetPage = Math.max(1, Math.min(numPages, page))
    addDebugLog(`Jumping to page ${targetPage}`)
    setPageNumber(targetPage)
  }, [numPages, addDebugLog])

  // Zoom functions with fit mode integration
  const zoomIn = useCallback(() => {
    setFitMode('custom')
    setScale(prev => {
      const newScale = Math.min(5.0, prev + 0.25)
      addDebugLog(`Zooming in to ${(newScale * 100).toFixed(0)}%`)
      return newScale
    })
  }, [addDebugLog])

  const zoomOut = useCallback(() => {
    setFitMode('custom')
    setScale(prev => {
      const newScale = Math.max(0.25, prev - 0.25)
      addDebugLog(`Zooming out to ${(newScale * 100).toFixed(0)}%`)
      return newScale
    })
  }, [addDebugLog])

  const setZoomLevel = useCallback((newScale: number) => {
    setFitMode('custom')
    setScale(Math.max(0.25, Math.min(5.0, newScale)))
    addDebugLog(`Setting zoom to ${(newScale * 100).toFixed(0)}%`)
  }, [addDebugLog])

  // Rotation function
  const rotateClockwise = useCallback(() => {
    setRotation(prev => {
      const newRotation = (prev + 90) % 360
      addDebugLog(`Rotating to ${newRotation} degrees`)
      // Clear cache when rotating as dimensions change
      setPageCache(new Map())
      return newRotation
    })
  }, [addDebugLog])

  // Fit mode functions
  const fitToWidth = useCallback(() => {
    setFitMode('width')
    addDebugLog('Fitting to width')
  }, [addDebugLog])



  const fitToPage = useCallback(() => {
    setFitMode('page')
    addDebugLog('Fitting to page')
  }, [addDebugLog])

  // View mode functions
  const setSinglePageView = useCallback(() => {
    setViewMode('single')
    addDebugLog('Switching to single page view')
  }, [addDebugLog])

  const setGridView = useCallback(() => {
    setViewMode('grid')
    addDebugLog('Switching to grid view')
  }, [addDebugLog])

  // Quality control
  const changeRenderQuality = useCallback((quality: RenderQuality) => {
    setRenderQuality(quality)
    addDebugLog(`Changing render quality to ${quality.name} (${quality.scale}x)`)
    // Update service render scale
    if (canvasPDFService) {
      canvasPDFService.setRenderScale(quality.scale)
    }
  }, [addDebugLog, canvasPDFService])

  // Editing mode functions
  const toggleEditingMode = useCallback(() => {
    setIsEditingMode(prev => {
      const newMode = !prev
      addDebugLog(`${newMode ? 'Enabling' : 'Disabling'} editing mode`)
      return newMode
    })
  }, [addDebugLog])

  // Annotation management
  const handleAnnotationAdd = useCallback((annotation: CanvasAnnotation) => {
    setAnnotations(prev => [...prev, annotation])
    addDebugLog(`Added ${annotation.type} annotation to page ${annotation.pageNumber}`)
  }, [addDebugLog])



  const clearAllAnnotations = useCallback(() => {
    setAnnotations([])
    addDebugLog('Cleared all annotations')
  }, [addDebugLog])

  const getPageAnnotations = useCallback((pageNum: number) => {
    if (canvasPDFService) {
      return canvasPDFService.getPageAnnotations(pageNum)
    }
    return annotations.filter(ann => ann.pageNumber === pageNum)
  }, [annotations, canvasPDFService])

  // Export annotations as JSON
  const exportAnnotations = useCallback(() => {
    const annotationData = {
      version: '1.0',
      created: new Date().toISOString(),
      annotations: annotations,
      pageCount: numPages
    }

    const blob = new Blob([JSON.stringify(annotationData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'pdf-annotations.json'
    a.click()
    URL.revokeObjectURL(url)

    addDebugLog(`Exported ${annotations.length} annotations`)
  }, [annotations, numPages, addDebugLog])

  const handleDownload = useCallback(() => {
    const url = URL.createObjectURL(file)
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [file])

  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (onPageClick) {
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      onPageClick(pageNumber, x, y)
    }
  }, [onPageClick, pageNumber])

  if (isLoading) {
    return (
      <div className="pdf-viewer-loading">
        <div className="loading-spinner"></div>
        <p>Loading PDF with Canvas Renderer...</p>
        {process.env.NODE_ENV === 'development' && (
          <details className="debug-details">
            <summary>Debug Information ({debugLogs.length} logs)</summary>
            <div className="debug-info">
              {debugLogs.map((log, index) => (
                <div key={index} className="debug-log">{log}</div>
              ))}
            </div>
          </details>
        )}
      </div>
    )
  }

  if (error) {
    return (
      <div className="pdf-viewer-error">
        <AlertTriangle size={48} className="error-icon" />
        <h3>Canvas PDF Viewer Error</h3>
        <p>{error}</p>
        
        <div className="error-actions">
          <button onClick={handleDownload} className="download-button">
            <Download size={20} />
            Download PDF
          </button>
          <button 
            onClick={() => {
              setError(null)
              setIsLoading(true)
            }}
            className="retry-button"
          >
            Retry
          </button>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="debug-details">
            <summary>Debug Information</summary>
            <div className="debug-info">
              {debugLogs.map((log, index) => (
                <div key={index} className="debug-log">{log}</div>
              ))}
            </div>
          </details>
        )}
      </div>
    )
  }

  return (
    <div className="pdf-viewer-container">
      {/* Enhanced Control Bar */}
      <div className="pdf-controls">
        <div className="navigation-controls">
          <button onClick={goToPrevPage} disabled={pageNumber <= 1} className="control-button">
            <ChevronLeft size={20} />
          </button>

          <div className="page-input-group">
            <input
              type="number"
              min="1"
              max={numPages}
              value={pageNumber}
              onChange={(e) => goToPage(parseInt(e.target.value) || 1)}
              className="page-input"
            />
            <span className="page-total">of {numPages}</span>
          </div>

          <button onClick={goToNextPage} disabled={pageNumber >= numPages} className="control-button">
            <ChevronRight size={20} />
          </button>
        </div>

        <div className="zoom-controls">
          <button onClick={zoomOut} className="control-button" title="Zoom Out">
            <ZoomOut size={20} />
          </button>

          <select
            value={Math.round(scale * 100)}
            onChange={(e) => setZoomLevel(parseInt(e.target.value) / 100)}
            className="zoom-select"
          >
            <option value="25">25%</option>
            <option value="50">50%</option>
            <option value="75">75%</option>
            <option value="100">100%</option>
            <option value="125">125%</option>
            <option value="150">150%</option>
            <option value="200">200%</option>
            <option value="300">300%</option>
            <option value="400">400%</option>
            <option value="500">500%</option>
          </select>

          <button onClick={zoomIn} className="control-button" title="Zoom In">
            <ZoomIn size={20} />
          </button>
        </div>

        <div className="fit-controls">
          <button
            onClick={fitToWidth}
            className={`control-button ${fitMode === 'width' ? 'active' : ''}`}
            title="Fit to Width"
          >
            Fit Width
          </button>
          <button
            onClick={fitToPage}
            className={`control-button ${fitMode === 'page' ? 'active' : ''}`}
            title="Fit to Page"
          >
            <Maximize2 size={16} />
          </button>
        </div>

        <div className="view-controls">
          <button
            onClick={setSinglePageView}
            className={`control-button ${viewMode === 'single' ? 'active' : ''}`}
            title="Single Page View"
          >
            Single
          </button>
          <button
            onClick={setGridView}
            className={`control-button ${viewMode === 'grid' ? 'active' : ''}`}
            title="Grid View"
          >
            <Grid3X3 size={16} />
          </button>
        </div>

        <div className="transform-controls">
          <button onClick={rotateClockwise} className="control-button" title="Rotate Clockwise">
            <RotateCw size={20} />
          </button>
        </div>

        <div className="quality-controls">
          <select
            value={renderQuality.name}
            onChange={(e) => {
              const quality = qualityOptions.find(q => q.name === e.target.value)
              if (quality) changeRenderQuality(quality)
            }}
            className="quality-select"
            title="Render Quality"
          >
            {qualityOptions.map(quality => (
              <option key={quality.name} value={quality.name}>
                {quality.name} ({quality.scale}x)
              </option>
            ))}
          </select>
        </div>

        <div className="editing-controls">
          <button
            onClick={toggleEditingMode}
            className={`control-button ${isEditingMode ? 'active' : ''}`}
            title="Toggle Editing Mode"
          >
            <Edit size={20} />
          </button>
          {annotations.length > 0 && (
            <>
              <button onClick={exportAnnotations} className="control-button" title="Export Annotations">
                Export
              </button>
              <button onClick={clearAllAnnotations} className="control-button" title="Clear All Annotations">
                Clear
              </button>
            </>
          )}
        </div>

        <div className="download-controls">
          <button onClick={handleDownload} className="control-button" title="Download PDF">
            <Download size={20} />
          </button>
        </div>

        {isRendering && (
          <div className="rendering-indicator">
            <div className="spinner"></div>
            <span>Rendering...</span>
          </div>
        )}
      </div>

      {/* Document Display Area */}
      <div className="pdf-document-container" ref={containerRef}>
        {viewMode === 'single' && (
          <div className="canvas-container" style={{ position: 'relative' }}>
            <canvas
              ref={canvasRef}
              onClick={!isEditingMode ? handleCanvasClick : undefined}
              className="pdf-canvas"
              style={{
                maxWidth: '100%',
                height: 'auto',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                cursor: isEditingMode ? 'crosshair' : 'pointer',
                background: 'white'
              }}
              onLoad={() => {
                if (canvasRef.current) {
                  setCanvasDisplaySize({
                    width: canvasRef.current.width,
                    height: canvasRef.current.height
                  })
                }
              }}
            />

            {/* Editing Overlay */}
            {isEditingMode && canvasDisplaySize.width > 0 && (
              <CanvasEditingOverlay
                pageNumber={pageNumber}
                canvasWidth={canvasDisplaySize.width}
                canvasHeight={canvasDisplaySize.height}
                scale={scale}
                onAnnotationAdd={handleAnnotationAdd}
                annotations={annotations}
                isActive={isEditingMode}
              />
            )}
          </div>
        )}

        {viewMode === 'grid' && (
          <div className="grid-container">
            {Array.from({ length: numPages }, (_, i) => i + 1).map(page => {
              const pageData = canvasPDFService?.getCachedPage(page)
              return (
                <div
                  key={page}
                  className={`grid-page ${page === pageNumber ? 'active' : ''}`}
                  onClick={() => goToPage(page)}
                >
                  {pageData && pageData.rendered ? (
                    <canvas
                      width={pageData.originalWidth * 0.2}
                      height={pageData.originalHeight * 0.2}
                      ref={canvas => {
                        if (canvas && pageData.canvas) {
                          const ctx = canvas.getContext('2d')
                          if (ctx) {
                            ctx.drawImage(
                              pageData.canvas,
                              0, 0, pageData.originalWidth, pageData.originalHeight,
                              0, 0, canvas.width, canvas.height
                            )
                          }
                        }
                      }}
                      className="grid-canvas"
                    />
                  ) : (
                    <div className="grid-placeholder">
                      <div className="loading-spinner"></div>
                      <span>Page {page}</span>
                    </div>
                  )}
                  <div className="grid-page-number">Page {page}</div>
                </div>
              )
            })}
          </div>
        )}

        {/* Status Information */}
        <div className="status-bar">
          <span>Canvas PDF Service: {canvasPDFService ? 'Ready' : 'Loading'}</span>
          <span>Quality: {renderQuality.name}</span>
          <span>Scale: {(scale * 100).toFixed(0)}%</span>
          <span>Rotation: {rotation}°</span>
          <span>Pages: {numPages}</span>
          <span>Cached: {pageCache.size}</span>
          {canvasPDFService && (
            <span>Memory: {canvasPDFService.getMemoryStats().totalElements} elements</span>
          )}
          {isEditingMode && (
            <>
              <span>Editing: ON</span>
              <span>Annotations: {annotations.length}</span>
              <span>Page Annotations: {getPageAnnotations(pageNumber).length}</span>
            </>
          )}
        </div>

        {process.env.NODE_ENV === 'development' && (
          <div className="debug-panel">
            <details>
              <summary>Canvas Renderer Debug ({debugLogs.length} logs)</summary>
              <div className="debug-info">
                {debugLogs.map((log, index) => (
                  <div key={index} className="debug-log">{log}</div>
                ))}
              </div>
            </details>
          </div>
        )}
      </div>
    </div>
  )
}

export default CanvasPDFViewer
