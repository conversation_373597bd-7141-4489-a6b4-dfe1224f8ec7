// Console health check utility for detecting remaining issues

interface ConsoleIssue {
  type: 'error' | 'warning' | 'deprecation' | 'memory-leak'
  message: string
  source?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  fix?: string
}

interface HealthCheckResult {
  isHealthy: boolean
  issues: ConsoleIssue[]
  score: number // 0-100
  recommendations: string[]
}

class ConsoleHealthChecker {
  private issues: ConsoleIssue[] = []
  private originalConsole = {
    error: console.error,
    warn: console.warn,
    log: console.log
  }

  startHealthCheck(): void {
    this.issues = []
    this.interceptConsole()
  }

  private interceptConsole(): void {
    // Intercept console.error
    console.error = (...args) => {
      const message = args.join(' ')
      this.analyzeError(message)
      this.originalConsole.error.apply(console, args)
    }

    // Intercept console.warn
    console.warn = (...args) => {
      const message = args.join(' ')
      this.analyzeWarning(message)
      this.originalConsole.warn.apply(console, args)
    }
  }

  private analyzeError(message: string): void {
    // Check for common React errors
    if (message.includes('Warning: Each child in a list should have a unique "key" prop')) {
      this.issues.push({
        type: 'error',
        message: 'Missing React keys in list rendering',
        severity: 'medium',
        fix: 'Add unique key props to list items'
      })
    }

    // Check for memory leaks
    if (message.includes('memory') || message.includes('leak')) {
      this.issues.push({
        type: 'memory-leak',
        message: 'Potential memory leak detected',
        severity: 'high',
        fix: 'Check for uncleaned event listeners or timers'
      })
    }

    // Check for PDF.js errors
    if (message.includes('PDF') || message.includes('pdfjs')) {
      this.issues.push({
        type: 'error',
        message: 'PDF.js related error',
        severity: 'medium',
        fix: 'Check PDF file validity and PDF.js configuration'
      })
    }

    // Check for network errors
    if (message.includes('fetch') || message.includes('network') || message.includes('CORS')) {
      this.issues.push({
        type: 'error',
        message: 'Network or CORS error',
        severity: 'high',
        fix: 'Check network connectivity and CORS configuration'
      })
    }
  }

  private analyzeWarning(message: string): void {
    // Check for deprecated API usage
    if (message.includes('deprecated') || message.includes('substr')) {
      this.issues.push({
        type: 'deprecation',
        message: 'Deprecated API usage detected',
        severity: 'low',
        fix: 'Update to use modern API alternatives'
      })
    }

    // Check for React warnings
    if (message.includes('Warning:')) {
      this.issues.push({
        type: 'warning',
        message: 'React warning detected',
        severity: 'medium',
        fix: 'Review React component implementation'
      })
    }

    // Check for performance warnings
    if (message.includes('performance') || message.includes('slow')) {
      this.issues.push({
        type: 'warning',
        message: 'Performance warning detected',
        severity: 'medium',
        fix: 'Optimize component rendering or data processing'
      })
    }
  }

  stopHealthCheck(): HealthCheckResult {
    // Restore original console methods
    console.error = this.originalConsole.error
    console.warn = this.originalConsole.warn

    return this.generateReport()
  }

  private generateReport(): HealthCheckResult {
    const criticalIssues = this.issues.filter(i => i.severity === 'critical').length
    const highIssues = this.issues.filter(i => i.severity === 'high').length
    const mediumIssues = this.issues.filter(i => i.severity === 'medium').length
    const lowIssues = this.issues.filter(i => i.severity === 'low').length

    // Calculate health score (0-100)
    let score = 100
    score -= criticalIssues * 25
    score -= highIssues * 15
    score -= mediumIssues * 10
    score -= lowIssues * 5
    score = Math.max(0, score)

    const isHealthy = score >= 80 && criticalIssues === 0

    const recommendations: string[] = []
    
    if (criticalIssues > 0) {
      recommendations.push('🚨 Fix critical issues immediately')
    }
    if (highIssues > 0) {
      recommendations.push('⚠️ Address high-severity issues')
    }
    if (mediumIssues > 0) {
      recommendations.push('📝 Review medium-severity warnings')
    }
    if (lowIssues > 0) {
      recommendations.push('🔧 Consider fixing low-severity issues')
    }
    if (isHealthy) {
      recommendations.push('✅ Console is healthy!')
    }

    return {
      isHealthy,
      issues: this.issues,
      score,
      recommendations
    }
  }

  // Quick health check without interception
  static quickCheck(): HealthCheckResult {
    const issues: ConsoleIssue[] = []
    
    // Check for common global issues
    if (typeof window !== 'undefined') {
      // Check for global error handlers
      if (!window.onerror && !window.addEventListener) {
        issues.push({
          type: 'warning',
          message: 'No global error handlers detected',
          severity: 'medium',
          fix: 'Add global error handling for better error tracking'
        })
      }

      // Check for memory usage (if available)
      if ('performance' in window && 'memory' in (window.performance as any)) {
        const memory = (window.performance as any).memory
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
          issues.push({
            type: 'memory-leak',
            message: 'High memory usage detected',
            severity: 'high',
            fix: 'Check for memory leaks and optimize memory usage'
          })
        }
      }
    }

    const score = issues.length === 0 ? 100 : Math.max(0, 100 - issues.length * 10)
    
    return {
      isHealthy: score >= 80,
      issues,
      score,
      recommendations: score >= 80 ? ['✅ Quick check passed'] : ['🔍 Run full health check for detailed analysis']
    }
  }
}

// Export functions
export const startConsoleHealthCheck = (): ConsoleHealthChecker => {
  const checker = new ConsoleHealthChecker()
  checker.startHealthCheck()
  return checker
}

export const quickConsoleHealthCheck = (): HealthCheckResult => {
  return ConsoleHealthChecker.quickCheck()
}

// Development helper
export const runConsoleHealthCheck = (durationMs: number = 10000): Promise<HealthCheckResult> => {
  return new Promise((resolve) => {
    const checker = startConsoleHealthCheck()
    
    setTimeout(() => {
      const result = checker.stopHealthCheck()
      resolve(result)
    }, durationMs)
  })
}

// Auto-check in development
if (process.env.NODE_ENV === 'development') {
  // Add global health check function
  ;(window as any).consoleHealthCheck = {
    quick: quickConsoleHealthCheck,
    run: runConsoleHealthCheck,
    start: startConsoleHealthCheck
  }
}
