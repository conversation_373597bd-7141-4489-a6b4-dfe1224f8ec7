import React, { useState, useEffect, useRef } from 'react'
import { X, MessageSquare } from 'lucide-react'

interface CommentModalProps {
  isOpen: boolean
  x: number
  y: number
  initialText?: string
  onSave: (text: string) => void
  onCancel: () => void
}

const CommentModal: React.FC<CommentModalProps> = ({
  isOpen,
  x,
  y,
  initialText = '',
  onSave,
  onCancel
}) => {
  const [text, setText] = useState(initialText)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus()
      textareaRef.current.select()
    }
  }, [isOpen])

  useEffect(() => {
    setText(initialText)
  }, [initialText])

  const handleSave = () => {
    if (text.trim()) {
      onSave(text.trim())
    } else {
      onCancel()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  if (!isOpen) return null

  return (
    <>
      <div className="modal-overlay" onClick={onCancel} />
      <div 
        className="comment-modal"
        style={{
          position: 'absolute',
          left: Math.min(x, (typeof window !== 'undefined' ? window.innerWidth : 1200) - 300),
          top: Math.min(y, (typeof window !== 'undefined' ? window.innerHeight : 800) - 200),
          zIndex: 1000
        }}
      >
        <div className="modal-header">
          <div className="header-icon">
            <MessageSquare size={20} />
            <h3>Add Comment</h3>
          </div>
          <button onClick={onCancel} className="close-button">
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          <textarea
            ref={textareaRef}
            value={text}
            onChange={(e) => setText(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter your comment here... (Ctrl+Enter to save)"
            rows={4}
            className="comment-input"
          />
        </div>

        <div className="modal-footer">
          <button onClick={onCancel} className="cancel-button">
            Cancel
          </button>
          <button onClick={handleSave} className="save-button">
            Add Comment
          </button>
        </div>
      </div>
    </>
  )
}

export default CommentModal
