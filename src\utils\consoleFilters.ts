// Console warning and error filters

interface ConsoleFilter {
  pattern: RegExp | string
  level: 'warn' | 'error' | 'log'
  suppress: boolean
  reason?: string
}

// Known harmless warnings that can be suppressed
const KNOWN_HARMLESS_WARNINGS: ConsoleFilter[] = [
  {
    pattern: /Warning: validateDOMNesting/,
    level: 'warn',
    suppress: true,
    reason: 'React DOM nesting warnings are often false positives in complex components'
  },
  {
    pattern: /Warning: Each child in a list should have a unique "key" prop/,
    level: 'warn',
    suppress: false, // Keep this as it's important for performance
    reason: 'Key prop warnings should be fixed'
  },
  {
    pattern: /Warning: Failed prop type/,
    level: 'warn',
    suppress: false, // Keep this as it indicates type issues
    reason: 'Prop type warnings indicate potential bugs'
  },
  {
    pattern: /PDF\.js/,
    level: 'warn',
    suppress: true,
    reason: 'PDF.js internal warnings are usually harmless'
  },
  {
    pattern: /pdfjs-dist/,
    level: 'warn',
    suppress: true,
    reason: 'PDF.js distribution warnings are usually harmless'
  },
  {
    pattern: /Warning: React does not recognize the/,
    level: 'warn',
    suppress: true,
    reason: 'Unknown DOM props warnings from third-party libraries'
  },
  {
    pattern: /Warning: componentWillReceiveProps has been renamed/,
    level: 'warn',
    suppress: true,
    reason: 'Legacy lifecycle method warnings from third-party libraries'
  },
  {
    pattern: /Warning: componentWillMount has been renamed/,
    level: 'warn',
    suppress: true,
    reason: 'Legacy lifecycle method warnings from third-party libraries'
  }
]

class ConsoleManager {
  private originalConsole: {
    log: typeof console.log
    warn: typeof console.warn
    error: typeof console.error
  }
  
  private suppressedCount = {
    log: 0,
    warn: 0,
    error: 0
  }

  constructor() {
    this.originalConsole = {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console)
    }
  }

  setupFilters(): void {
    // Only apply filters in production or when explicitly enabled
    const shouldFilter = process.env.NODE_ENV === 'production' || 
                        localStorage.getItem('enableConsoleFilters') === 'true'

    if (!shouldFilter) {
      return
    }

    this.interceptConsoleMethod('warn')
    this.interceptConsoleMethod('error')
    this.interceptConsoleMethod('log')
  }

  private interceptConsoleMethod(level: 'log' | 'warn' | 'error'): void {
    const originalMethod = this.originalConsole[level]
    
    console[level] = (...args: unknown[]) => {
      const message = args.join(' ')
      
      // Check if this message should be suppressed
      const filter = KNOWN_HARMLESS_WARNINGS.find(filter => 
        filter.level === level && this.matchesPattern(message, filter.pattern)
      )

      if (filter && filter.suppress) {
        this.suppressedCount[level]++
        
        // In development, show a summary of suppressed messages periodically
        if (process.env.NODE_ENV === 'development' && this.suppressedCount[level] % 10 === 0) {
          originalMethod(`[Console Filter] Suppressed ${this.suppressedCount[level]} ${level} messages. Reason: ${filter.reason}`)
        }
        
        return
      }

      // Call the original method for non-suppressed messages
      originalMethod.apply(console, args)
    }
  }

  private matchesPattern(message: string, pattern: RegExp | string): boolean {
    if (pattern instanceof RegExp) {
      return pattern.test(message)
    }
    return message.includes(pattern)
  }

  getSuppressedCounts(): typeof this.suppressedCount {
    return { ...this.suppressedCount }
  }

  resetCounts(): void {
    this.suppressedCount = { log: 0, warn: 0, error: 0 }
  }

  restoreOriginalConsole(): void {
    console.log = this.originalConsole.log
    console.warn = this.originalConsole.warn
    console.error = this.originalConsole.error
  }

  // Method to temporarily enable/disable filtering
  setFilteringEnabled(enabled: boolean): void {
    if (enabled) {
      localStorage.setItem('enableConsoleFilters', 'true')
      this.setupFilters()
    } else {
      localStorage.removeItem('enableConsoleFilters')
      this.restoreOriginalConsole()
    }
  }
}

// Singleton instance
const consoleManager = new ConsoleManager()

// Export functions
export const setupConsoleFilters = (): void => {
  consoleManager.setupFilters()
}

export const getSuppressedCounts = () => {
  return consoleManager.getSuppressedCounts()
}

export const resetSuppressedCounts = (): void => {
  consoleManager.resetCounts()
}

export const restoreOriginalConsole = (): void => {
  consoleManager.restoreOriginalConsole()
}

export const setConsoleFilteringEnabled = (enabled: boolean): void => {
  consoleManager.setFilteringEnabled(enabled)
}

// Development helper to show current filter status
export const showConsoleFilterStatus = (): void => {
  const counts = getSuppressedCounts()
  console.group('Console Filter Status')
  console.log('Suppressed messages:', counts)
  console.log('Filtering enabled:', localStorage.getItem('enableConsoleFilters') === 'true')
  console.log('Environment:', process.env.NODE_ENV)
  console.groupEnd()
}

// Auto-setup in production
if (process.env.NODE_ENV === 'production') {
  setupConsoleFilters()
}
