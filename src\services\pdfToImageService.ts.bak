/**
 * PDF to Image Conversion Service
 * Converts PDF pages to high-quality images for editing
 * Uses multiple fallback methods for maximum compatibility
 */

export interface PDFPageImage {
  pageNumber: number
  imageData: string // Base64 data URL
  width: number
  height: number
  scale: number
}

export interface ConversionOptions {
  scale: number // Default: 2.0 for high quality
  format: 'png' | 'jpeg'
  quality: number // 0.1 to 1.0 for JPEG
  maxWidth?: number
  maxHeight?: number
}

export interface ConversionProgress {
  currentPage: number
  totalPages: number
  status: 'converting' | 'complete' | 'error'
  message: string
}

export class PDFToImageService {
  private static instance: PDFToImageService
  private conversionMethod: 'pdfjs' | 'canvas' | 'fallback' = 'pdfjs'

  static getInstance(): PDFToImageService {
    if (!PDFToImageService.instance) {
      PDFToImageService.instance = new PDFToImageService()
    }
    return PDFToImageService.instance
  }

  /**
   * Convert entire PDF to images
   */
  async convertPDFToImages(
    file: File,
    options: ConversionOptions = { scale: 2.0, format: 'png', quality: 0.9 },
    onProgress?: (progress: ConversionProgress) => void
  ): Promise<PDFPageImage[]> {
    const results: PDFPageImage[] = []
    
    try {
      // Try PDF.js method first
      const pdfImages = await this.convertWithPDFJS(file, options, onProgress)
      if (pdfImages.length > 0) {
        this.conversionMethod = 'pdfjs'
        return pdfImages
      }
    } catch (error) {
      console.warn('PDF.js conversion failed, trying canvas method:', error)
    }

    try {
      // Fallback to canvas method
      const canvasImages = await this.convertWithCanvas(file, options, onProgress)
      if (canvasImages.length > 0) {
        this.conversionMethod = 'canvas'
        return canvasImages
      }
    } catch (error) {
      console.warn('Canvas conversion failed, using fallback:', error)
    }

    // Final fallback - create placeholder images
    return this.createFallbackImages(file, options, onProgress)
  }

  /**
   * Method 1: Convert using PDF.js (most reliable)
   */
  private async convertWithPDFJS(
    file: File,
    options: ConversionOptions,
    onProgress?: (progress: ConversionProgress) => void
  ): Promise<PDFPageImage[]> {
    // Dynamic import to avoid loading issues
    const pdfjsLib = await import('pdfjs-dist')
    
    // Set worker path to local file
    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf-worker/pdf.worker.min.js'

    const arrayBuffer = await file.arrayBuffer()
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
    const numPages = pdf.numPages
    const results: PDFPageImage[] = []

    onProgress?.({
      currentPage: 0,
      totalPages: numPages,
      status: 'converting',
      message: 'Starting PDF.js conversion...'
    })

    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum)
        const viewport = page.getViewport({ scale: options.scale })
        
        // Create canvas
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')!
        canvas.width = viewport.width
        canvas.height = viewport.height

        // Render page to canvas
        await page.render({
          canvasContext: context,
          viewport: viewport
        }).promise

        // Convert to image data
        const imageData = canvas.toDataURL(
          options.format === 'jpeg' ? 'image/jpeg' : 'image/png',
          options.quality
        )

        results.push({
          pageNumber: pageNum,
          imageData,
          width: viewport.width,
          height: viewport.height,
          scale: options.scale
        })

        onProgress?.({
          currentPage: pageNum,
          totalPages: numPages,
          status: 'converting',
          message: `Converted page ${pageNum} of ${numPages}`
        })

      } catch (error) {
        console.error(`Error converting page ${pageNum}:`, error)
        // Continue with other pages
      }
    }

    onProgress?.({
      currentPage: numPages,
      totalPages: numPages,
      status: 'complete',
      message: `Successfully converted ${results.length} pages`
    })

    return results
  }

  /**
   * Method 2: Convert using Canvas API (fallback)
   */
  private async convertWithCanvas(
    file: File,
    options: ConversionOptions,
    onProgress?: (progress: ConversionProgress) => void
  ): Promise<PDFPageImage[]> {
    // This method would use a different approach
    // For now, return empty array to trigger final fallback
    onProgress?.({
      currentPage: 0,
      totalPages: 1,
      status: 'error',
      message: 'Canvas method not yet implemented'
    })
    return []
  }

  /**
   * Method 3: Create fallback images (last resort)
   */
  private async createFallbackImages(
    file: File,
    options: ConversionOptions,
    onProgress?: (progress: ConversionProgress) => void
  ): Promise<PDFPageImage[]> {
    onProgress?.({
      currentPage: 0,
      totalPages: 1,
      status: 'converting',
      message: 'Creating fallback representation...'
    })

    // Create a placeholder image representing the PDF
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = 800 * options.scale
    canvas.height = 1000 * options.scale
    
    // Draw placeholder content
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    ctx.fillStyle = '#333333'
    ctx.font = `${24 * options.scale}px Arial`
    ctx.textAlign = 'center'
    ctx.fillText('PDF Content', canvas.width / 2, canvas.height / 2 - 50)
    ctx.fillText(file.name, canvas.width / 2, canvas.height / 2)
    ctx.fillText('Click to edit', canvas.width / 2, canvas.height / 2 + 50)
    
    // Add border
    ctx.strokeStyle = '#cccccc'
    ctx.lineWidth = 2
    ctx.strokeRect(0, 0, canvas.width, canvas.height)

    const imageData = canvas.toDataURL(
      options.format === 'jpeg' ? 'image/jpeg' : 'image/png',
      options.quality
    )

    onProgress?.({
      currentPage: 1,
      totalPages: 1,
      status: 'complete',
      message: 'Fallback representation created'
    })

    return [{
      pageNumber: 1,
      imageData,
      width: canvas.width,
      height: canvas.height,
      scale: options.scale
    }]
  }

  /**
   * Get current conversion method being used
   */
  getConversionMethod(): string {
    return this.conversionMethod
  }

  /**
   * Test if PDF.js is available and working
   */
  async testPDFJSAvailability(): Promise<boolean> {
    try {
      const pdfjsLib = await import('pdfjs-dist')
      return !!pdfjsLib.getDocument
    } catch {
      return false
    }
  }
}

export default PDFToImageService
