import React, { useState, useCallback } from 'react'

interface HighlightToolProps {
  isActive: boolean
  color: string
  onHighlight: (x: number, y: number, width: number, height: number, pageNumber: number) => void
}

interface SelectionRect {
  startX: number
  startY: number
  endX: number
  endY: number
  pageNumber: number
}

const HighlightTool: React.FC<HighlightToolProps> = ({ isActive, color, onHighlight }) => {
  const [isSelecting, setIsSelecting] = useState(false)
  const [selectionRect, setSelectionRect] = useState<SelectionRect | null>(null)


  const handleMouseDown = useCallback((e: React.MouseEvent, pageNumber: number) => {
    if (!isActive) return

    e.preventDefault()
    const rect = e.currentTarget.getBoundingClientRect()
    const startX = e.clientX - rect.left
    const startY = e.clientY - rect.top

    setIsSelecting(true)
    setSelectionRect({
      startX,
      startY,
      endX: startX,
      endY: startY,
      pageNumber
    })
  }, [isActive])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isSelecting || !selectionRect) return

    const rect = e.currentTarget.getBoundingClientRect()
    const endX = e.clientX - rect.left
    const endY = e.clientY - rect.top

    setSelectionRect(prev => prev ? {
      ...prev,
      endX,
      endY
    } : null)
  }, [isSelecting, selectionRect])

  const handleMouseUp = useCallback(() => {
    if (!isSelecting || !selectionRect) return

    const { startX, startY, endX, endY, pageNumber } = selectionRect
    
    // Calculate the highlight rectangle
    const x = Math.min(startX, endX)
    const y = Math.min(startY, endY)
    const width = Math.abs(endX - startX)
    const height = Math.abs(endY - startY)

    // Only create highlight if the selection is large enough
    if (width > 5 && height > 5) {
      onHighlight(x, y, width, height, pageNumber)
    }

    setIsSelecting(false)
    setSelectionRect(null)
  }, [isSelecting, selectionRect, onHighlight])

  const renderSelectionOverlay = () => {
    if (!isSelecting || !selectionRect) return null

    const { startX, startY, endX, endY } = selectionRect
    const x = Math.min(startX, endX)
    const y = Math.min(startY, endY)
    const width = Math.abs(endX - startX)
    const height = Math.abs(endY - startY)

    return (
      <div
        className="highlight-selection-overlay"
        style={{
          position: 'absolute',
          left: x,
          top: y,
          width,
          height,
          backgroundColor: color,
          opacity: 0.3,
          pointerEvents: 'none',
          zIndex: 10
        }}
      />
    )
  }

  return (
    <div
      className={`highlight-tool ${isActive ? 'active' : ''}`}
      onMouseDown={(e) => handleMouseDown(e, 1)} // TODO: Get actual page number
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        cursor: isActive ? 'crosshair' : 'default',
        zIndex: isActive ? 5 : -1
      }}
    >
      {renderSelectionOverlay()}
    </div>
  )
}

export default HighlightTool
