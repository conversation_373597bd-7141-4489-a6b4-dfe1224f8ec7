import React, { useCallback, useState } from 'react'
import { Upload, FileText, AlertCircle } from 'lucide-react'

interface FileUploadProps {
  onFileSelect: (file: File) => void
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileSelect }) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const validateFile = (file: File): boolean => {
    if (file.type !== 'application/pdf') {
      setError('Please select a PDF file')
      return false
    }
    
    // Check file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      setError('File size must be less than 50MB')
      return false
    }
    
    setError(null)
    return true
  }

  const handleFileSelect = useCallback((file: File) => {
    if (validateFile(file)) {
      onFileSelect(file)
    }
  }, [onFileSelect])

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  return (
    <div className="file-upload-container" itemScope itemType="https://schema.org/SoftwareApplication">
      {/* Hero Upload Section - Above the fold */}
      <section className="hero-upload-section">
        <div className="upload-hero-content">
          <h2>Start Editing Your PDF</h2>
          <p>Upload your PDF file to begin editing instantly. No account required.</p>
        </div>

        <div
        className={`file-upload-area ${isDragOver ? 'drag-over' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        role="button"
        tabIndex={0}
        aria-label="Upload PDF file area"
      >
        <div className="upload-content">
          <Upload size={48} className="upload-icon" aria-hidden="true" />
          <h3>Upload PDF Document</h3>
          <p>Drag and drop your PDF file here, or click to browse</p>

          <input
            type="file"
            accept=".pdf,application/pdf"
            onChange={handleFileInputChange}
            className="file-input"
            id="file-input"
            aria-describedby="file-info"
          />
          <label htmlFor="file-input" className="file-input-label">
            <FileText size={20} aria-hidden="true" />
            Choose File
          </label>

          <div className="file-info" id="file-info">
            <p>Supported format: PDF</p>
            <p>Maximum size: 50MB</p>
            <p>✓ No registration required</p>
            <p>✓ Files processed locally in your browser</p>
          </div>
        </div>
      </div>

      {error && (
        <div className="error-message" role="alert" aria-live="polite">
          <AlertCircle size={20} aria-hidden="true" />
          <span>{error}</span>
        </div>
      )}
      </section>

      {/* Quick Feature Highlights */}
      <section className="quick-features">
        <div className="feature-highlights" itemScope itemType="https://schema.org/ItemList">
          <meta itemProp="numberOfItems" content="6" />
          <div className="feature-grid">
            <div className="feature-item" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
              <meta itemProp="position" content="1" />
              <span itemProp="name">✏️ Add Text</span>
            </div>
            <div className="feature-item" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
              <meta itemProp="position" content="2" />
              <span itemProp="name">🖍️ Highlight</span>
            </div>
            <div className="feature-item" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
              <meta itemProp="position" content="3" />
              <span itemProp="name">🎨 Draw</span>
            </div>
            <div className="feature-item" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
              <meta itemProp="position" content="4" />
              <span itemProp="name">🖼️ Insert Images</span>
            </div>
            <div className="feature-item" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
              <meta itemProp="position" content="5" />
              <span itemProp="name">💬 Add Comments</span>
            </div>
            <div className="feature-item" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
              <meta itemProp="position" content="6" />
              <span itemProp="name">🔒 100% Secure</span>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works-section">
        <h2>How to Edit PDF Files Online</h2>
        <div className="steps-grid">
          <div className="step-item" itemScope itemType="https://schema.org/HowToStep">
            <meta itemProp="position" content="1" />
            <div className="step-number">1</div>
            <h3 itemProp="name">Upload Your PDF</h3>
            <p itemProp="text">Select or drag and drop your PDF document. Files are processed securely in your browser.</p>
          </div>
          <div className="step-item" itemScope itemType="https://schema.org/HowToStep">
            <meta itemProp="position" content="2" />
            <div className="step-number">2</div>
            <h3 itemProp="name">Edit and Annotate</h3>
            <p itemProp="text">Use our comprehensive tools to add text, highlight content, draw, insert images, and add comments.</p>
          </div>
          <div className="step-item" itemScope itemType="https://schema.org/HowToStep">
            <meta itemProp="position" content="3" />
            <div className="step-number">3</div>
            <h3 itemProp="name">Save Your Changes</h3>
            <p itemProp="text">Download your edited PDF with all modifications applied. Your original document remains unchanged.</p>
          </div>
        </div>
      </section>

      {/* What is PDF Editing Section */}
      <section className="what-is-pdf-editing">
        <h2>What is PDF Editing?</h2>
        <div className="content-grid">
          <div className="content-block">
            <h3>Understanding PDF Documents</h3>
            <p>PDF (Portable Document Format) files are designed to preserve document formatting across different devices and platforms. Originally created by Adobe, PDFs have become the standard for sharing documents that need to maintain their visual integrity.</p>
          </div>
          <div className="content-block">
            <h3>Why Edit PDFs Online?</h3>
            <p>Online PDF editing eliminates the need for expensive software installations. Our browser-based editor provides professional-grade tools accessible from any device with an internet connection, making document editing more convenient and cost-effective.</p>
          </div>
          <div className="content-block">
            <h3>Common PDF Editing Use Cases</h3>
            <ul>
              <li>Filling out forms and applications</li>
              <li>Adding signatures to contracts</li>
              <li>Annotating research papers and documents</li>
              <li>Correcting typos and updating information</li>
              <li>Adding comments for collaboration</li>
              <li>Inserting images and logos</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Professional Features Section */}
      <section className="features-section">
        <h2>Professional PDF Editing Features</h2>
        <div className="features-list" itemScope itemType="https://schema.org/ItemList">
          <div className="feature-category">
            <h3>Text Editing Tools</h3>
            <ul>
              <li itemProp="itemListElement">Add custom text with various fonts and sizes</li>
              <li itemProp="itemListElement">Edit existing text content</li>
              <li itemProp="itemListElement">Change text color and formatting</li>
              <li itemProp="itemListElement">Adjust text alignment and spacing</li>
            </ul>
          </div>
          <div className="feature-category">
            <h3>Annotation Tools</h3>
            <ul>
              <li itemProp="itemListElement">Highlight important sections</li>
              <li itemProp="itemListElement">Add comments and notes</li>
              <li itemProp="itemListElement">Draw freehand annotations</li>
              <li itemProp="itemListElement">Insert shapes and arrows</li>
            </ul>
          </div>
          <div className="feature-category">
            <h3>Advanced Features</h3>
            <ul>
              <li itemProp="itemListElement">Insert images and graphics</li>
              <li itemProp="itemListElement">Undo/Redo functionality</li>
              <li itemProp="itemListElement">Secure browser-based processing</li>
              <li itemProp="itemListElement">Multiple file format support</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section className="comparison-section">
        <h2>Why Choose Open PDF Editor?</h2>
        <div className="comparison-table">
          <table>
            <thead>
              <tr>
                <th>Feature</th>
                <th>Open PDF Editor</th>
                <th>Adobe Acrobat</th>
                <th>Smallpdf</th>
                <th>PDFtk</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Price</td>
                <td>✅ Free</td>
                <td>❌ $12.99/month</td>
                <td>⚠️ Limited free</td>
                <td>✅ Free</td>
              </tr>
              <tr>
                <td>Browser-based</td>
                <td>✅ Yes</td>
                <td>❌ Desktop only</td>
                <td>✅ Yes</td>
                <td>❌ Command line</td>
              </tr>
              <tr>
                <td>No registration</td>
                <td>✅ Yes</td>
                <td>❌ Account required</td>
                <td>❌ Account required</td>
                <td>✅ Yes</td>
              </tr>
              <tr>
                <td>File privacy</td>
                <td>✅ Local processing</td>
                <td>⚠️ Cloud sync</td>
                <td>❌ Server upload</td>
                <td>✅ Local processing</td>
              </tr>
              <tr>
                <td>Text editing</td>
                <td>✅ Yes</td>
                <td>✅ Yes</td>
                <td>⚠️ Limited</td>
                <td>❌ No</td>
              </tr>
              <tr>
                <td>Annotations</td>
                <td>✅ Yes</td>
                <td>✅ Yes</td>
                <td>✅ Yes</td>
                <td>❌ No</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="advantages-grid">
          <div className="advantage-item">
            <h3>🚀 Instant Access</h3>
            <p>No downloads or installations required. Start editing PDFs immediately in your browser.</p>
          </div>
          <div className="advantage-item">
            <h3>🔒 Complete Privacy</h3>
            <p>Your files never leave your device. All processing happens locally in your browser.</p>
          </div>
          <div className="advantage-item">
            <h3>💰 Always Free</h3>
            <p>No hidden costs, subscriptions, or premium features. Everything is free forever.</p>
          </div>
          <div className="advantage-item">
            <h3>🌐 Cross-Platform</h3>
            <p>Works on Windows, Mac, Linux, iOS, and Android. Any device with a modern browser.</p>
          </div>
        </div>
      </section>

      {/* Comprehensive FAQ Section */}
      <section className="faq-section" itemScope itemType="https://schema.org/FAQPage">
        <h2>Frequently Asked Questions</h2>

        {/* Technical Issues FAQ */}
        <div className="faq-category">
          <h3>Technical Issues</h3>
          <div className="faq-list">
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">Is this PDF editor free to use?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">Yes, our PDF editor is completely free to use. No registration, subscription, or hidden fees required.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">What browsers are supported?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">Our PDF editor works on all modern browsers including Chrome, Firefox, Safari, and Edge. We recommend using the latest version for the best experience.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">What is the maximum file size limit?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">You can upload PDF files up to 50MB in size. For larger files, consider compressing them first or splitting them into smaller documents.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">Why won't my PDF load?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">PDF loading issues can occur due to corrupted files, password protection, or browser compatibility. Try using a different browser or ensure your PDF isn't password-protected.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Security & Privacy FAQ */}
        <div className="faq-category">
          <h3>Security & Privacy</h3>
          <div className="faq-list">
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">Are my PDF files secure?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">Absolutely. All PDF processing happens locally in your browser. Your files never leave your device, ensuring complete privacy and security.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">Do you store my files on your servers?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">No, we never store your files on our servers. All editing happens in your browser using client-side JavaScript, ensuring your documents remain private.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">Can I use this for confidential documents?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">Yes, since all processing is done locally in your browser, it's safe to use for confidential documents. No data is transmitted to external servers.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Features FAQ */}
        <div className="faq-category">
          <h3>Features & Usage</h3>
          <div className="faq-list">
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">What editing features are available?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">You can add text, highlight content, draw annotations, insert images, add comments, and more. Our editor provides comprehensive tools for most PDF editing needs.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">Can I edit text in existing PDFs?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">You can add new text and annotations to PDFs. Editing existing text depends on how the PDF was created and may have limitations.</p>
              </div>
            </div>
            <div className="faq-item" itemScope itemType="https://schema.org/Question">
              <h4 itemProp="name">What file formats are supported?</h4>
              <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                <p itemProp="text">Currently, we support PDF files. You can export your edited documents as PDF files. Support for additional formats may be added in future updates.</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default FileUpload
