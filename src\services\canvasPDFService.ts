/**
 * Canvas PDF Service
 * Handles PDF to Canvas conversion, canvas-based editing, and Canvas to PDF conversion
 * This service provides a unified workflow for canvas-based PDF editing
 */

import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'
import * as pdfjsLib from 'pdfjs-dist'

// Canvas page data structure
export interface CanvasPageData {
  pageNumber: number
  canvas: HTMLCanvasElement
  originalWidth: number
  originalHeight: number
  scale: number
  rendered: boolean
}

// Edit element interfaces (reusing from existing service)
export interface TextElement {
  id: string
  x: number
  y: number
  text: string
  fontSize: number
  color: string
  fontFamily: string
  pageNumber: number
}

export interface HighlightElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  color: string
  pageNumber: number
}

export interface CommentElement {
  id: string
  x: number
  y: number
  text: string
  pageNumber: number
}

export interface DrawElement {
  id: string
  points: { x: number; y: number }[]
  strokeWidth: number
  color: string
  pageNumber: number
}

export interface ImageElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  imageData: string
  rotation: number
  pageNumber: number
}

export type EditElement = TextElement | HighlightElement | CommentElement | DrawElement | ImageElement

// Canvas editing annotation for overlay
export interface CanvasAnnotation {
  id: string
  type: 'text' | 'highlight' | 'draw' | 'comment' | 'image' | 'shape'
  pageNumber: number
  x: number
  y: number
  width?: number
  height?: number
  content?: string
  color?: string
  fontSize?: number
  strokeWidth?: number
  points?: { x: number; y: number }[]
  shapeType?: 'rectangle' | 'circle' | 'arrow'
  timestamp: number
}

export class CanvasPDFService {
  private pdfDocument: any = null
  private pageCache: Map<number, CanvasPageData> = new Map()
  private editElements: EditElement[] = []
  private canvasAnnotations: CanvasAnnotation[] = []
  private renderScale: number = 2.0

  /**
   * Load PDF file and prepare for canvas-based editing
   */
  async loadPDF(file: File): Promise<{ numPages: number; success: boolean }> {
    try {
      const arrayBuffer = await file.arrayBuffer()
      
      // Load PDF with pdf.js for canvas rendering
      const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer })
      this.pdfDocument = await loadingTask.promise
      
      const numPages = this.pdfDocument.numPages
      
      // Clear any existing cache
      this.pageCache.clear()
      this.editElements = []
      this.canvasAnnotations = []
      
      return { numPages, success: true }
    } catch (error) {
      console.error('Failed to load PDF:', error)
      return { numPages: 0, success: false }
    }
  }

  /**
   * Render a specific page to canvas
   */
  async renderPageToCanvas(pageNumber: number, targetCanvas?: HTMLCanvasElement): Promise<CanvasPageData | null> {
    if (!this.pdfDocument) {
      throw new Error('PDF not loaded')
    }

    try {
      // Check cache first
      const cached = this.pageCache.get(pageNumber)
      if (cached && !targetCanvas) {
        return cached
      }

      const page = await this.pdfDocument.getPage(pageNumber)
      const viewport = page.getViewport({ scale: this.renderScale })

      // Create or use provided canvas
      const canvas = targetCanvas || document.createElement('canvas')
      const context = canvas.getContext('2d')

      if (!context) {
        throw new Error('Failed to get canvas context')
      }

      canvas.height = viewport.height
      canvas.width = viewport.width

      // High-quality rendering settings
      context.imageSmoothingEnabled = true
      context.imageSmoothingQuality = 'high'

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
        intent: 'display',
        enableWebGL: false,
        renderInteractiveForms: false,
        transform: null,
        background: 'white'
      }

      await page.render(renderContext).promise

      const pageData: CanvasPageData = {
        pageNumber,
        canvas,
        originalWidth: viewport.width,
        originalHeight: viewport.height,
        scale: this.renderScale,
        rendered: true
      }

      // Cache the result if not using target canvas
      if (!targetCanvas) {
        this.pageCache.set(pageNumber, pageData)
      }

      return pageData
    } catch (error) {
      console.error(`Failed to render page ${pageNumber}:`, error)
      return null
    }
  }

  /**
   * Get cached page data
   */
  getCachedPage(pageNumber: number): CanvasPageData | null {
    return this.pageCache.get(pageNumber) || null
  }

  /**
   * Add text element to canvas
   */
  async addTextElement(element: Omit<TextElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const textElement: TextElement = { ...element, id }
    
    this.editElements.push(textElement)
    
    // Also add as canvas annotation for overlay rendering
    const annotation: CanvasAnnotation = {
      id,
      type: 'text',
      pageNumber: element.pageNumber,
      x: element.x,
      y: element.y,
      content: element.text,
      color: element.color,
      fontSize: element.fontSize,
      timestamp: Date.now()
    }
    
    this.canvasAnnotations.push(annotation)
    
    return id
  }

  /**
   * Add highlight element to canvas
   */
  async addHighlightElement(element: Omit<HighlightElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const highlightElement: HighlightElement = { ...element, id }
    
    this.editElements.push(highlightElement)
    
    const annotation: CanvasAnnotation = {
      id,
      type: 'highlight',
      pageNumber: element.pageNumber,
      x: element.x,
      y: element.y,
      width: element.width,
      height: element.height,
      color: element.color,
      timestamp: Date.now()
    }
    
    this.canvasAnnotations.push(annotation)
    
    return id
  }

  /**
   * Add draw element to canvas
   */
  async addDrawElement(element: Omit<DrawElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const drawElement: DrawElement = { ...element, id }
    
    this.editElements.push(drawElement)
    
    const annotation: CanvasAnnotation = {
      id,
      type: 'draw',
      pageNumber: element.pageNumber,
      x: element.points[0]?.x || 0,
      y: element.points[0]?.y || 0,
      points: element.points,
      color: element.color,
      strokeWidth: element.strokeWidth,
      timestamp: Date.now()
    }
    
    this.canvasAnnotations.push(annotation)
    
    return id
  }

  /**
   * Add comment element to canvas
   */
  async addCommentElement(element: Omit<CommentElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const commentElement: CommentElement = { ...element, id }
    
    this.editElements.push(commentElement)
    
    const annotation: CanvasAnnotation = {
      id,
      type: 'comment',
      pageNumber: element.pageNumber,
      x: element.x,
      y: element.y,
      content: element.text,
      timestamp: Date.now()
    }
    
    this.canvasAnnotations.push(annotation)
    
    return id
  }

  /**
   * Add image element to canvas
   */
  async addImageElement(element: Omit<ImageElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const imageElement: ImageElement = { ...element, id }
    
    this.editElements.push(imageElement)
    
    const annotation: CanvasAnnotation = {
      id,
      type: 'image',
      pageNumber: element.pageNumber,
      x: element.x,
      y: element.y,
      width: element.width,
      height: element.height,
      timestamp: Date.now()
    }
    
    this.canvasAnnotations.push(annotation)
    
    return id
  }

  /**
   * Get all canvas annotations for a specific page
   */
  getPageAnnotations(pageNumber: number): CanvasAnnotation[] {
    return this.canvasAnnotations.filter(annotation => annotation.pageNumber === pageNumber)
  }

  /**
   * Get all edit elements
   */
  getEditElements(): EditElement[] {
    return [...this.editElements]
  }

  /**
   * Delete an element by ID
   */
  deleteElement(id: string): void {
    this.editElements = this.editElements.filter(el => el.id !== id)
    this.canvasAnnotations = this.canvasAnnotations.filter(annotation => annotation.id !== id)
  }

  /**
   * Set render scale for canvas rendering
   */
  setRenderScale(scale: number): void {
    this.renderScale = scale
    // Clear cache to force re-render at new scale
    this.pageCache.clear()
  }

  /**
   * Get current render scale
   */
  getRenderScale(): number {
    return this.renderScale
  }

  /**
   * Generate a modified PDF with all canvas edits applied
   */
  async generateModifiedPDF(): Promise<Uint8Array> {
    if (!this.pdfDocument) {
      throw new Error('PDF not loaded')
    }

    try {
      // Create a new PDF document
      const newPdfDoc = await PDFDocument.create()

      // Process each page
      for (let pageNum = 1; pageNum <= this.pdfDocument.numPages; pageNum++) {
        await this.addPageToPDF(newPdfDoc, pageNum)
      }

      return await newPdfDoc.save()
    } catch (error) {
      console.error('Failed to generate modified PDF:', error)
      throw new Error('Failed to generate PDF')
    }
  }

  /**
   * Add a single page with edits to the new PDF
   */
  private async addPageToPDF(pdfDoc: PDFDocument, pageNumber: number): Promise<void> {
    // Get the rendered canvas for this page
    const pageData = await this.renderPageToCanvas(pageNumber)
    if (!pageData) {
      throw new Error(`Failed to render page ${pageNumber}`)
    }

    // Create a new page in the PDF
    const page = pdfDoc.addPage([pageData.originalWidth / this.renderScale, pageData.originalHeight / this.renderScale])

    // Convert canvas to image and embed in PDF
    const canvasImageData = pageData.canvas.toDataURL('image/png')
    const imageBytes = this.dataUrlToBytes(canvasImageData)
    const image = await pdfDoc.embedPng(imageBytes)

    // Draw the canvas image as background
    page.drawImage(image, {
      x: 0,
      y: 0,
      width: page.getWidth(),
      height: page.getHeight()
    })

    // Add edit elements as vector graphics for better quality
    await this.addEditElementsToPage(pdfDoc, page, pageNumber)
  }

  /**
   * Add edit elements as vector graphics to PDF page
   */
  private async addEditElementsToPage(pdfDoc: PDFDocument, page: any, pageNumber: number): Promise<void> {
    const pageElements = this.editElements.filter(el => el.pageNumber === pageNumber)

    for (const element of pageElements) {
      try {
        if ('text' in element && 'fontSize' in element) {
          await this.renderTextElementToPDF(pdfDoc, page, element as TextElement)
        } else if ('width' in element && 'height' in element && !('imageData' in element)) {
          await this.renderHighlightElementToPDF(page, element as HighlightElement)
        } else if ('text' in element && !('fontSize' in element)) {
          await this.renderCommentElementToPDF(pdfDoc, page, element as CommentElement)
        } else if ('points' in element) {
          await this.renderDrawElementToPDF(page, element as DrawElement)
        } else if ('imageData' in element) {
          await this.renderImageElementToPDF(pdfDoc, page, element as ImageElement)
        }
      } catch (error) {
        console.warn(`Failed to render element ${element.id}:`, error)
      }
    }
  }

  /**
   * Render text element to PDF page
   */
  private async renderTextElementToPDF(pdfDoc: PDFDocument, page: any, element: TextElement): Promise<void> {
    const color = this.hexToRgb(element.color)
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica)

    // Convert coordinates (canvas uses top-left, PDF uses bottom-left)
    const { height } = page.getSize()
    const pdfY = height - (element.y / this.renderScale) - (element.fontSize / this.renderScale)

    page.drawText(element.text, {
      x: element.x / this.renderScale,
      y: pdfY,
      size: element.fontSize / this.renderScale,
      font: font,
      color: rgb(color.r, color.g, color.b)
    })
  }

  /**
   * Render highlight element to PDF page
   */
  private async renderHighlightElementToPDF(page: any, element: HighlightElement): Promise<void> {
    const color = this.hexToRgb(element.color)
    const { height } = page.getSize()
    const pdfY = height - (element.y / this.renderScale) - (element.height / this.renderScale)

    page.drawRectangle({
      x: element.x / this.renderScale,
      y: pdfY,
      width: element.width / this.renderScale,
      height: element.height / this.renderScale,
      color: rgb(color.r, color.g, color.b),
      opacity: 0.3
    })
  }

  /**
   * Render comment element to PDF page
   */
  private async renderCommentElementToPDF(pdfDoc: PDFDocument, page: any, element: CommentElement): Promise<void> {
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica)
    const { height } = page.getSize()
    const pdfY = height - (element.y / this.renderScale) - 12

    // Draw comment icon
    page.drawCircle({
      x: element.x / this.renderScale,
      y: pdfY,
      size: 8 / this.renderScale,
      color: rgb(1, 0.8, 0),
      borderColor: rgb(1, 0.6, 0),
      borderWidth: 1 / this.renderScale
    })

    // Draw comment text
    page.drawText(element.text, {
      x: (element.x + 20) / this.renderScale,
      y: pdfY - 6,
      size: 10 / this.renderScale,
      font: font,
      color: rgb(0, 0, 0)
    })
  }

  /**
   * Render draw element to PDF page
   */
  private async renderDrawElementToPDF(page: any, element: DrawElement): Promise<void> {
    if (element.points.length < 2) return

    const color = this.hexToRgb(element.color)
    const { height } = page.getSize()

    // Draw lines connecting the points
    for (let i = 0; i < element.points.length - 1; i++) {
      const startPoint = element.points[i]
      const endPoint = element.points[i + 1]

      const startY = height - (startPoint.y / this.renderScale)
      const endY = height - (endPoint.y / this.renderScale)

      page.drawLine({
        start: { x: startPoint.x / this.renderScale, y: startY },
        end: { x: endPoint.x / this.renderScale, y: endY },
        thickness: element.strokeWidth / this.renderScale,
        color: rgb(color.r, color.g, color.b)
      })
    }
  }

  /**
   * Render image element to PDF page
   */
  private async renderImageElementToPDF(pdfDoc: PDFDocument, page: any, element: ImageElement): Promise<void> {
    try {
      const imageBytes = this.dataUrlToBytes(element.imageData)

      let image
      if (element.imageData.startsWith('data:image/png')) {
        image = await pdfDoc.embedPng(imageBytes)
      } else if (element.imageData.startsWith('data:image/jpeg') || element.imageData.startsWith('data:image/jpg')) {
        image = await pdfDoc.embedJpg(imageBytes)
      } else {
        image = await pdfDoc.embedPng(imageBytes)
      }

      const { height } = page.getSize()
      const pdfY = height - (element.y / this.renderScale) - (element.height / this.renderScale)

      page.drawImage(image, {
        x: element.x / this.renderScale,
        y: pdfY,
        width: element.width / this.renderScale,
        height: element.height / this.renderScale,
        rotate: this.degreesToRadians(element.rotation)
      })
    } catch (error) {
      console.error('Error rendering image to PDF:', error)
    }
  }

  /**
   * Convert hex color to RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    } : { r: 0, g: 0, b: 0 }
  }

  /**
   * Convert data URL to bytes
   */
  private dataUrlToBytes(dataUrl: string): Uint8Array {
    try {
      const parts = dataUrl.split(',')
      if (parts.length !== 2) {
        throw new Error('Invalid data URL format')
      }

      const base64 = parts[1]
      if (!base64) {
        throw new Error('No base64 data found in data URL')
      }

      const binaryString = atob(base64)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      return bytes
    } catch (error) {
      console.error('Error converting data URL to bytes:', error)
      throw new Error('Failed to process image data')
    }
  }

  /**
   * Convert degrees to radians
   */
  private degreesToRadians(degrees: number): number {
    return (degrees * Math.PI) / 180
  }
}
