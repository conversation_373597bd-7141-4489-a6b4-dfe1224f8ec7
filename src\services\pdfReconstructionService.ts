/**
 * PDF Reconstruction Service
 * Combines edited images with overlay elements back into a new PDF document
 */

import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'
// import { PDFPageImage } from './pdfToImageService'

interface PDFPageImage {
  pageNumber: number
  imageData: string
  width: number
  height: number
}

export interface EditElement {
  id: string
  type: 'text' | 'highlight' | 'comment' | 'drawing' | 'image'
  pageNumber: number
  x: number
  y: number
  width?: number
  height?: number
  content?: string
  style?: {
    fontSize?: number
    color?: string
    fontFamily?: string
    backgroundColor?: string
    strokeWidth?: number
  }
}

export interface ReconstructionOptions {
  includeOriginalImages: boolean
  imageQuality: number // 0.1 to 1.0
  compressImages: boolean
  embedFonts: boolean
}

export class PDFReconstructionService {
  private static instance: PDFReconstructionService

  static getInstance(): PDFReconstructionService {
    if (!PDFReconstructionService.instance) {
      PDFReconstructionService.instance = new PDFReconstructionService()
    }
    return PDFReconstructionService.instance
  }

  /**
   * Reconstruct PDF from edited images and overlay elements
   */
  async reconstructPDF(
    pdfImages: PDFPageImage[],
    editElements: EditElement[],
    options: ReconstructionOptions = {
      includeOriginalImages: true,
      imageQuality: 0.9,
      compressImages: true,
      embedFonts: true
    }
  ): Promise<Uint8Array> {
    const pdfDoc = await PDFDocument.create()
    
    // Embed standard font
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica)
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold)

    for (const pageImage of pdfImages) {
      // Create new page
      const page = pdfDoc.addPage([pageImage.width, pageImage.height])
      
      if (options.includeOriginalImages) {
        // Embed the page image
        try {
          const imageBytes = this.dataURLToBytes(pageImage.imageData)
          const image = pageImage.imageData.includes('data:image/png') 
            ? await pdfDoc.embedPng(imageBytes)
            : await pdfDoc.embedJpg(imageBytes)
          
          // Draw the background image
          page.drawImage(image, {
            x: 0,
            y: 0,
            width: pageImage.width,
            height: pageImage.height
          })
        } catch (error) {
          console.warn(`Failed to embed image for page ${pageImage.pageNumber}:`, error)
        }
      }

      // Add edit elements for this page
      const pageElements = editElements.filter(el => el.pageNumber === pageImage.pageNumber)
      
      for (const element of pageElements) {
        try {
          await this.addElementToPage(page, element, font, boldFont, pageImage.height)
        } catch (error) {
          console.warn(`Failed to add element ${element.id}:`, error)
        }
      }
    }

    return await pdfDoc.save()
  }

  /**
   * Add a single edit element to a PDF page
   */
  private async addElementToPage(
    page: any,
    element: EditElement,
    font: any,
    _boldFont: any,
    pageHeight: number
  ): Promise<void> {
    // Convert coordinates (canvas uses top-left origin, PDF uses bottom-left)
    const pdfY = pageHeight - element.y

    switch (element.type) {
      case 'text':
        if (element.content) {
          const fontSize = element.style?.fontSize || 16
          const color = this.hexToRgb(element.style?.color || '#000000')
          
          page.drawText(element.content, {
            x: element.x,
            y: pdfY - fontSize, // Adjust for text baseline
            size: fontSize,
            font: font,
            color: rgb(color.r, color.g, color.b)
          })
        }
        break

      case 'comment':
        if (element.content) {
          const fontSize = (element.style?.fontSize || 12)
          const color = this.hexToRgb(element.style?.color || '#ff0000')
          
          // Draw comment background
          page.drawRectangle({
            x: element.x - 5,
            y: pdfY - fontSize - 5,
            width: element.content.length * fontSize * 0.6 + 10,
            height: fontSize + 10,
            color: rgb(1, 1, 0.8),
            opacity: 0.8
          })
          
          // Draw comment text
          page.drawText(element.content, {
            x: element.x,
            y: pdfY - fontSize,
            size: fontSize,
            font: font,
            color: rgb(color.r, color.g, color.b)
          })
        }
        break

      case 'highlight':
        const highlightColor = this.hexToRgb(element.style?.backgroundColor || '#ffff00')
        page.drawRectangle({
          x: element.x,
          y: pdfY - (element.height || 20),
          width: element.width || 100,
          height: element.height || 20,
          color: rgb(highlightColor.r, highlightColor.g, highlightColor.b),
          opacity: 0.5
        })
        break

      case 'drawing':
        if (element.content) {
          try {
            const path = JSON.parse(element.content) as {x: number, y: number}[]
            if (path.length > 1) {
              // For now, draw lines between points
              // In a more advanced implementation, you could use SVG paths
              const strokeColor = this.hexToRgb(element.style?.color || '#000000')
              const strokeWidth = element.style?.strokeWidth || 2
              
              for (let i = 1; i < path.length; i++) {
                const startY = pageHeight - path[i-1].y
                const endY = pageHeight - path[i].y
                
                // Draw line segment (simplified - PDF-lib doesn't have direct line drawing)
                page.drawRectangle({
                  x: Math.min(path[i-1].x, path[i].x),
                  y: Math.min(startY, endY),
                  width: Math.max(strokeWidth, Math.abs(path[i].x - path[i-1].x)),
                  height: Math.max(strokeWidth, Math.abs(endY - startY)),
                  color: rgb(strokeColor.r, strokeColor.g, strokeColor.b)
                })
              }
            }
          } catch (error) {
            console.warn('Failed to parse drawing path:', error)
          }
        }
        break

      case 'image':
        // Image embedding would be implemented here
        // For now, just draw a placeholder rectangle
        page.drawRectangle({
          x: element.x,
          y: pdfY - (element.height || 100),
          width: element.width || 100,
          height: element.height || 100,
          color: rgb(0.9, 0.9, 0.9),
          borderColor: rgb(0.5, 0.5, 0.5),
          borderWidth: 1
        })
        break
    }
  }

  /**
   * Convert hex color to RGB values
   */
  private hexToRgb(hex: string): { r: number, g: number, b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    } : { r: 0, g: 0, b: 0 }
  }

  /**
   * Convert data URL to bytes
   */
  private dataURLToBytes(dataURL: string): Uint8Array {
    const base64 = dataURL.split(',')[1]
    const binaryString = atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    
    return bytes
  }

  /**
   * Create a canvas with edited content and convert to image
   */
  async createEditedPageImage(
    originalImage: PDFPageImage,
    pageElements: EditElement[],
    scale: number = 1.0
  ): Promise<string> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = originalImage.width * scale
    canvas.height = originalImage.height * scale
    
    // Draw original image
    const img = new Image()
    
    return new Promise((resolve) => {
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        // Draw edit elements
        pageElements.forEach(element => {
          ctx.save()
          
          switch (element.type) {
            case 'text':
            case 'comment':
              ctx.fillStyle = element.style?.color || '#000000'
              ctx.font = `${(element.style?.fontSize || 16) * scale}px Arial`
              ctx.fillText(element.content || '', element.x * scale, element.y * scale)
              break
              
            case 'highlight':
              ctx.fillStyle = element.style?.backgroundColor || '#ffff0080'
              ctx.fillRect(
                element.x * scale,
                element.y * scale,
                (element.width || 100) * scale,
                (element.height || 20) * scale
              )
              break
              
            case 'drawing':
              if (element.content) {
                const path = JSON.parse(element.content) as {x: number, y: number}[]
                if (path.length > 1) {
                  ctx.strokeStyle = element.style?.color || '#000000'
                  ctx.lineWidth = (element.style?.strokeWidth || 2) * scale
                  ctx.beginPath()
                  ctx.moveTo(path[0].x * scale, path[0].y * scale)
                  path.slice(1).forEach(point => {
                    ctx.lineTo(point.x * scale, point.y * scale)
                  })
                  ctx.stroke()
                }
              }
              break
          }
          
          ctx.restore()
        })
        
        resolve(canvas.toDataURL('image/png', 0.9))
      }
      
      img.src = originalImage.imageData
    })
  }
}

export default PDFReconstructionService
