// Define EditElement locally to avoid import issues
interface TextElement {
  id: string
  x: number
  y: number
  text: string
  fontSize: number
  color: string
  fontFamily: string
  pageNumber: number
}

interface HighlightElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  color: string
  pageNumber: number
}

interface CommentElement {
  id: string
  x: number
  y: number
  text: string
  pageNumber: number
}

interface DrawElement {
  id: string
  points: { x: number; y: number }[]
  strokeWidth: number
  color: string
  pageNumber: number
}

interface ImageElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  imageData: string
  rotation: number
  pageNumber: number
}

type EditElement = TextElement | HighlightElement | CommentElement | DrawElement | ImageElement

export interface UndoRedoState {
  elements: EditElement[]
  timestamp: number
  description: string
}

export class UndoRedoService {
  private undoStack: UndoRedoState[] = []
  private redoStack: UndoRedoState[] = []
  private maxHistorySize: number = 50

  constructor(maxHistorySize: number = 50) {
    this.maxHistorySize = maxHistorySize
  }

  saveState(elements: EditElement[], description: string): void {
    const state: UndoRedoState = {
      elements: JSON.parse(JSON.stringify(elements)), // Deep clone
      timestamp: Date.now(),
      description
    }

    this.undoStack.push(state)
    
    // Limit history size
    if (this.undoStack.length > this.maxHistorySize) {
      this.undoStack.shift()
    }

    // Clear redo stack when new action is performed
    this.redoStack = []
  }

  undo(): EditElement[] | null {
    if (this.undoStack.length === 0) return null

    const currentState = this.undoStack.pop()!
    this.redoStack.push(currentState)

    // Return the previous state, or empty array if no previous state
    const previousState = this.undoStack[this.undoStack.length - 1]
    return previousState ? [...previousState.elements] : []
  }

  redo(): EditElement[] | null {
    if (this.redoStack.length === 0) return null

    const stateToRestore = this.redoStack.pop()!
    this.undoStack.push(stateToRestore)

    return [...stateToRestore.elements]
  }

  canUndo(): boolean {
    return this.undoStack.length > 0
  }

  canRedo(): boolean {
    return this.redoStack.length > 0
  }

  getUndoDescription(): string | null {
    if (this.undoStack.length === 0) return null
    return this.undoStack[this.undoStack.length - 1].description
  }

  getRedoDescription(): string | null {
    if (this.redoStack.length === 0) return null
    return this.redoStack[this.redoStack.length - 1].description
  }

  getHistorySize(): number {
    return this.undoStack.length
  }

  getRedoSize(): number {
    return this.redoStack.length
  }

  clear(): void {
    this.undoStack = []
    this.redoStack = []
  }

  getHistory(): UndoRedoState[] {
    return [...this.undoStack]
  }

  getRedoHistory(): UndoRedoState[] {
    return [...this.redoStack]
  }
}
