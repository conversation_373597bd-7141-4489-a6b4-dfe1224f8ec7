import React from 'react'
// Define ToolType locally to avoid import issues
type ToolType = 'select' | 'text' | 'highlight' | 'comment' | 'image' | 'draw' | 'erase'
import {
  MousePointer,
  Type,
  Highlighter,
  MessageSquare,
  Image,
  Pen,
  Eraser
} from 'lucide-react'

interface StatusBarProps {
  activeTool: ToolType
  currentPage: number
  totalPages: number
  zoomLevel: number
  editCount: number
  fileName: string
}

const StatusBar: React.FC<StatusBarProps> = ({
  activeTool,
  currentPage,
  totalPages,
  zoomLevel,
  editCount,
  fileName
}) => {
  const getToolIcon = (tool: ToolType) => {
    const iconProps = { size: 16 }
    switch (tool) {
      case 'select': return <MousePointer {...iconProps} />
      case 'text': return <Type {...iconProps} />
      case 'highlight': return <Highlighter {...iconProps} />
      case 'comment': return <MessageSquare {...iconProps} />
      case 'image': return <Image {...iconProps} />
      case 'draw': return <Pen {...iconProps} />
      case 'erase': return <Eraser {...iconProps} />
      default: return <MousePointer {...iconProps} />
    }
  }

  const getToolName = (tool: ToolType) => {
    switch (tool) {
      case 'select': return 'Select'
      case 'text': return 'Text'
      case 'highlight': return 'Highlight'
      case 'comment': return 'Comment'
      case 'image': return 'Image'
      case 'draw': return 'Draw'
      case 'erase': return 'Erase'
      default: return 'Select'
    }
  }

  return (
    <div className="status-bar">
      <div className="status-section">
        <span className="status-label">File:</span>
        <span className="status-value" title={fileName}>
          {fileName.length > 30 ? `${fileName.substring(0, 30)}...` : fileName}
        </span>
      </div>

      <div className="status-section">
        <span className="status-label">Page:</span>
        <span className="status-value">
          {currentPage} of {totalPages}
        </span>
      </div>

      <div className="status-section">
        <span className="status-label">Zoom:</span>
        <span className="status-value">
          {Math.round(zoomLevel * 100)}%
        </span>
      </div>

      <div className="status-section">
        <span className="status-label">Tool:</span>
        <div className="status-tool">
          {getToolIcon(activeTool)}
          <span className="status-value">{getToolName(activeTool)}</span>
        </div>
      </div>

      <div className="status-section">
        <span className="status-label">Edits:</span>
        <span className="status-value">{editCount}</span>
      </div>

      <div className="status-section status-ready">
        <div className="status-indicator"></div>
        <span className="status-value">Ready</span>
      </div>
    </div>
  )
}

export default StatusBar
