import React, { useState } from 'react'
import {
  Type,
  Highlighter,
  MessageSquare,
  Image,
  Pen,
  Eraser,
  Undo,
  Redo,
  MousePointer
} from 'lucide-react'

// Define ToolType locally to avoid import issues
type ToolType = 'select' | 'text' | 'highlight' | 'comment' | 'image' | 'draw' | 'erase'

interface ToolbarProps {
  activeTool: ToolType
  onToolChange: (tool: ToolType) => void
  onUndo: () => void
  onRedo: () => void
  canUndo: boolean
  canRedo: boolean
  onSettingsChange?: (settings: any) => void
}

const Toolbar: React.FC<ToolbarProps> = ({
  activeTool,
  onToolChange,
  onUndo,
  onRedo,
  canUndo,
  canRedo
}) => {
  const [textSettings, setTextSettings] = useState({
    fontSize: 16,
    color: '#000000',
    fontFamily: 'Arial'
  })

  const [highlightColor, setHighlightColor] = useState('#ffff00')
  const [drawSettings, setDrawSettings] = useState({
    strokeWidth: 2,
    color: '#000000'
  })

  const tools = [
    { id: 'select' as ToolType, icon: MousePointer, label: 'Select' },
    { id: 'text' as ToolType, icon: Type, label: 'Add Text' },
    { id: 'highlight' as ToolType, icon: Highlighter, label: 'Highlight' },
    { id: 'comment' as ToolType, icon: MessageSquare, label: 'Comment' },
    { id: 'image' as ToolType, icon: Image, label: 'Add Image' },
    { id: 'draw' as ToolType, icon: Pen, label: 'Draw' },
    { id: 'erase' as ToolType, icon: Eraser, label: 'Erase' }
  ]

  return (
    <div className="toolbar">
      <div className="toolbar-section">
        <h3>Tools</h3>
        <div className="tool-buttons">
          {tools.map(tool => (
            <button
              key={tool.id}
              className={`tool-button ${activeTool === tool.id ? 'active' : ''}`}
              onClick={() => onToolChange(tool.id)}
              title={tool.label}
            >
              <tool.icon size={20} />
              <span>{tool.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="toolbar-section">
        <h3>Actions</h3>
        <div className="action-buttons">
          <button
            className="action-button"
            onClick={onUndo}
            disabled={!canUndo}
            title="Undo"
          >
            <Undo size={20} />
            <span>Undo</span>
          </button>
          <button
            className="action-button"
            onClick={onRedo}
            disabled={!canRedo}
            title="Redo"
          >
            <Redo size={20} />
            <span>Redo</span>
          </button>
        </div>
      </div>

      {activeTool === 'text' && (
        <div className="toolbar-section">
          <h3>Text Settings</h3>
          <div className="text-settings">
            <label>
              Font Size:
              <input
                type="number"
                min="8"
                max="72"
                value={textSettings.fontSize}
                onChange={(e) => setTextSettings(prev => ({
                  ...prev,
                  fontSize: parseInt(e.target.value)
                }))}
              />
            </label>
            
            <label>
              Color:
              <input
                type="color"
                value={textSettings.color}
                onChange={(e) => setTextSettings(prev => ({
                  ...prev,
                  color: e.target.value
                }))}
              />
            </label>
            
            <label>
              Font:
              <select
                value={textSettings.fontFamily}
                onChange={(e) => setTextSettings(prev => ({
                  ...prev,
                  fontFamily: e.target.value
                }))}
              >
                <option value="Arial">Arial</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Courier New">Courier New</option>
                <option value="Helvetica">Helvetica</option>
              </select>
            </label>
          </div>
        </div>
      )}

      {activeTool === 'highlight' && (
        <div className="toolbar-section">
          <h3>Highlight Settings</h3>
          <div className="highlight-settings">
            <label>
              Color:
              <input
                type="color"
                value={highlightColor}
                onChange={(e) => setHighlightColor(e.target.value)}
              />
            </label>
          </div>
        </div>
      )}

      {activeTool === 'draw' && (
        <div className="toolbar-section">
          <h3>Draw Settings</h3>
          <div className="draw-settings">
            <label>
              Stroke Width:
              <input
                type="range"
                min="1"
                max="10"
                value={drawSettings.strokeWidth}
                onChange={(e) => setDrawSettings(prev => ({
                  ...prev,
                  strokeWidth: parseInt(e.target.value)
                }))}
              />
              <span>{drawSettings.strokeWidth}px</span>
            </label>
            
            <label>
              Color:
              <input
                type="color"
                value={drawSettings.color}
                onChange={(e) => setDrawSettings(prev => ({
                  ...prev,
                  color: e.target.value
                }))}
              />
            </label>
          </div>
        </div>
      )}
    </div>
  )
}

export default Toolbar
