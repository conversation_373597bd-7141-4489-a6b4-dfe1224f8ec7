// Console monitoring and debugging utilities

interface ConsoleStats {
  errors: number
  warnings: number
  logs: number
  startTime: number
  lastActivity: number
}

class ConsoleMonitor {
  private stats: ConsoleStats = {
    errors: 0,
    warnings: 0,
    logs: 0,
    startTime: Date.now(),
    lastActivity: Date.now()
  }

  private originalMethods = {
    log: console.log,
    warn: console.warn,
    error: console.error
  }

  private isMonitoring = false

  startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.stats.startTime = Date.now()

    // Wrap console methods to track usage
    console.log = (...args) => {
      this.stats.logs++
      this.stats.lastActivity = Date.now()
      this.originalMethods.log.apply(console, args)
    }

    console.warn = (...args) => {
      this.stats.warnings++
      this.stats.lastActivity = Date.now()
      this.originalMethods.warn.apply(console, args)
    }

    console.error = (...args) => {
      this.stats.errors++
      this.stats.lastActivity = Date.now()
      this.originalMethods.error.apply(console, args)
    }

    // Log monitoring start in development
    if (process.env.NODE_ENV === 'development') {
      this.originalMethods.log('🔍 Console monitoring started')
    }
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) return

    this.isMonitoring = false

    // Restore original methods
    console.log = this.originalMethods.log
    console.warn = this.originalMethods.warn
    console.error = this.originalMethods.error

    if (process.env.NODE_ENV === 'development') {
      this.originalMethods.log('🔍 Console monitoring stopped')
    }
  }

  getStats(): ConsoleStats & { 
    uptime: number
    timeSinceLastActivity: number
    isHealthy: boolean
  } {
    const now = Date.now()
    const uptime = now - this.stats.startTime
    const timeSinceLastActivity = now - this.stats.lastActivity
    
    // Consider console "healthy" if there are no errors and warnings are minimal
    const isHealthy = this.stats.errors === 0 && this.stats.warnings < 5

    return {
      ...this.stats,
      uptime,
      timeSinceLastActivity,
      isHealthy
    }
  }

  generateReport(): string {
    const stats = this.getStats()
    const uptimeMinutes = Math.floor(stats.uptime / 60000)
    const uptimeSeconds = Math.floor((stats.uptime % 60000) / 1000)

    return `
📊 Console Health Report
========================
⏱️  Uptime: ${uptimeMinutes}m ${uptimeSeconds}s
❌ Errors: ${stats.errors}
⚠️  Warnings: ${stats.warnings}
📝 Logs: ${stats.logs}
💚 Status: ${stats.isHealthy ? 'Healthy' : 'Issues Detected'}
🕐 Last Activity: ${Math.floor(stats.timeSinceLastActivity / 1000)}s ago
    `.trim()
  }

  reset(): void {
    this.stats = {
      errors: 0,
      warnings: 0,
      logs: 0,
      startTime: Date.now(),
      lastActivity: Date.now()
    }
  }

  // Development helper to show report in console
  showReport(): void {
    if (process.env.NODE_ENV === 'development') {
      this.originalMethods.log(this.generateReport())
    }
  }

  // Auto-report every N minutes in development
  startAutoReporting(intervalMinutes: number = 5): () => void {
    if (process.env.NODE_ENV !== 'development') {
      return () => {} // No-op in production
    }

    const interval = setInterval(() => {
      this.showReport()
    }, intervalMinutes * 60 * 1000)

    return () => clearInterval(interval)
  }
}

// Singleton instance
const consoleMonitor = new ConsoleMonitor()

// Export functions
export const startConsoleMonitoring = (): void => {
  consoleMonitor.startMonitoring()
}

export const stopConsoleMonitoring = (): void => {
  consoleMonitor.stopMonitoring()
}

export const getConsoleStats = () => {
  return consoleMonitor.getStats()
}

export const showConsoleReport = (): void => {
  consoleMonitor.showReport()
}

export const resetConsoleStats = (): void => {
  consoleMonitor.reset()
}

export const startAutoReporting = (intervalMinutes?: number) => {
  return consoleMonitor.startAutoReporting(intervalMinutes)
}

// Development helper function
export const enableConsoleDebugging = (): void => {
  if (process.env.NODE_ENV !== 'development') return

  // Add global debugging functions
  ;(window as any).consoleDebug = {
    showReport: showConsoleReport,
    getStats: getConsoleStats,
    reset: resetConsoleStats,
    startMonitoring: startConsoleMonitoring,
    stopMonitoring: stopConsoleMonitoring
  }

  console.log('🐛 Console debugging enabled. Use window.consoleDebug for utilities.')
}

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development') {
  startConsoleMonitoring()
  enableConsoleDebugging()
}
