/**
 * Canvas Workflow Test Utility
 * Tests the complete PDF → Canvas → Edit → PDF workflow
 */

import { CanvasPDFService } from '../services/canvasPDFService'

export interface WorkflowTestResult {
  success: boolean
  steps: {
    pdfLoad: boolean
    canvasRender: boolean
    editOperations: boolean
    pdfGeneration: boolean
  }
  errors: string[]
  performance: {
    loadTime: number
    renderTime: number
    editTime: number
    generateTime: number
  }
}

export class CanvasWorkflowTester {
  private service: CanvasPDFService
  private testResults: WorkflowTestResult

  constructor() {
    this.service = new CanvasPDFService()
    this.testResults = {
      success: false,
      steps: {
        pdfLoad: false,
        canvasRender: false,
        editOperations: false,
        pdfGeneration: false
      },
      errors: [],
      performance: {
        loadTime: 0,
        renderTime: 0,
        editTime: 0,
        generateTime: 0
      }
    }
  }

  /**
   * Create a minimal test PDF using the existing test.pdf file
   */
  private async createTestPDF(): Promise<Uint8Array> {
    try {
      // Use the existing test.pdf file from public directory
      const response = await fetch('/test.pdf')
      if (!response.ok) {
        throw new Error('Failed to fetch test PDF')
      }
      return new Uint8Array(await response.arrayBuffer())
    } catch (error) {
      console.warn('Failed to load test.pdf, creating minimal PDF:', error)

      // Fallback: create a minimal valid PDF
      const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 <<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000350 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
433
%%EOF`

      return new TextEncoder().encode(pdfContent)
    }
  }

  /**
   * Test PDF loading step
   */
  private async testPDFLoad(): Promise<boolean> {
    try {
      const startTime = performance.now()

      // Create test PDF file
      const pdfBytes = await this.createTestPDF()
      const testFile = new File([pdfBytes], 'test.pdf', { type: 'application/pdf' })
      
      const result = await this.service.loadPDF(testFile)
      
      this.testResults.performance.loadTime = performance.now() - startTime
      
      if (result.success && result.numPages > 0) {
        this.testResults.steps.pdfLoad = true
        return true
      } else {
        this.testResults.errors.push('PDF load failed: Invalid result')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`PDF load failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Test canvas rendering step
   */
  private async testCanvasRender(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      const pageData = await this.service.renderPageToCanvas(1)
      
      this.testResults.performance.renderTime = performance.now() - startTime
      
      if (pageData && pageData.rendered && pageData.canvas) {
        this.testResults.steps.canvasRender = true
        return true
      } else {
        this.testResults.errors.push('Canvas render failed: No page data')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`Canvas render failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Test edit operations
   */
  private async testEditOperations(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      // Test adding text element
      const textId = await this.service.addTextElement({
        x: 100,
        y: 100,
        text: 'Test Text',
        fontSize: 16,
        color: '#000000',
        fontFamily: 'Arial',
        pageNumber: 1
      })

      // Test adding highlight element
      const highlightId = await this.service.addHighlightElement({
        x: 200,
        y: 200,
        width: 100,
        height: 20,
        color: '#ffff00',
        pageNumber: 1
      })

      // Test adding comment element
      const commentId = await this.service.addCommentElement({
        x: 300,
        y: 300,
        text: 'Test Comment',
        pageNumber: 1
      })

      this.testResults.performance.editTime = performance.now() - startTime

      const elements = this.service.getEditElements()
      
      if (elements.length === 3 && textId && highlightId && commentId) {
        this.testResults.steps.editOperations = true
        return true
      } else {
        this.testResults.errors.push('Edit operations failed: Incorrect element count')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`Edit operations failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Test PDF generation step
   */
  private async testPDFGeneration(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      const pdfBytes = await this.service.generateModifiedPDF()
      
      this.testResults.performance.generateTime = performance.now() - startTime
      
      if (pdfBytes && pdfBytes.length > 0) {
        this.testResults.steps.pdfGeneration = true
        return true
      } else {
        this.testResults.errors.push('PDF generation failed: No output')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`PDF generation failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Run complete workflow test
   */
  async runCompleteTest(): Promise<WorkflowTestResult> {
    console.log('Starting Canvas Workflow Test...')
    
    // Reset results
    this.testResults = {
      success: false,
      steps: {
        pdfLoad: false,
        canvasRender: false,
        editOperations: false,
        pdfGeneration: false
      },
      errors: [],
      performance: {
        loadTime: 0,
        renderTime: 0,
        editTime: 0,
        generateTime: 0
      }
    }

    // Run tests in sequence
    const pdfLoadSuccess = await this.testPDFLoad()
    console.log('PDF Load:', pdfLoadSuccess ? 'PASS' : 'FAIL')

    if (pdfLoadSuccess) {
      const canvasRenderSuccess = await this.testCanvasRender()
      console.log('Canvas Render:', canvasRenderSuccess ? 'PASS' : 'FAIL')

      if (canvasRenderSuccess) {
        const editOperationsSuccess = await this.testEditOperations()
        console.log('Edit Operations:', editOperationsSuccess ? 'PASS' : 'FAIL')

        if (editOperationsSuccess) {
          const pdfGenerationSuccess = await this.testPDFGeneration()
          console.log('PDF Generation:', pdfGenerationSuccess ? 'PASS' : 'FAIL')

          this.testResults.success = pdfGenerationSuccess
        }
      }
    }

    console.log('Canvas Workflow Test Complete:', this.testResults.success ? 'PASS' : 'FAIL')
    console.log('Performance:', this.testResults.performance)
    
    if (this.testResults.errors.length > 0) {
      console.log('Errors:', this.testResults.errors)
    }

    return this.testResults
  }

  /**
   * Get test results
   */
  getResults(): WorkflowTestResult {
    return { ...this.testResults }
  }
}

export default CanvasWorkflowTester
