/**
 * Canvas Workflow Test Utility
 * Tests the complete PDF → Canvas → Edit → PDF workflow
 */

import { CanvasPDFService } from '../services/canvasPDFService'

export interface WorkflowTestResult {
  success: boolean
  steps: {
    pdfLoad: boolean
    canvasRender: boolean
    editOperations: boolean
    pdfGeneration: boolean
  }
  errors: string[]
  performance: {
    loadTime: number
    renderTime: number
    editTime: number
    generateTime: number
  }
}

export class CanvasWorkflowTester {
  private service: CanvasPDFService
  private testResults: WorkflowTestResult

  constructor() {
    this.service = new CanvasPDFService()
    this.testResults = {
      success: false,
      steps: {
        pdfLoad: false,
        canvasRender: false,
        editOperations: false,
        pdfGeneration: false
      },
      errors: [],
      performance: {
        loadTime: 0,
        renderTime: 0,
        editTime: 0,
        generateTime: 0
      }
    }
  }

  /**
   * Create a minimal test PDF
   */
  private async createTestPDF(): Promise<Uint8Array> {
    // Create a minimal valid PDF in binary format
    return new Uint8Array([
      0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, 0x0a, // %PDF-1.4\n
      0x31, 0x20, 0x30, 0x20, 0x6f, 0x62, 0x6a, 0x0a,       // 1 0 obj\n
      0x3c, 0x3c, 0x2f, 0x54, 0x79, 0x70, 0x65, 0x2f,       // <</Type/
      0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f,       // Catalog/
      0x50, 0x61, 0x67, 0x65, 0x73, 0x20, 0x32, 0x20,       // Pages 2
      0x30, 0x20, 0x52, 0x3e, 0x3e, 0x0a, 0x65, 0x6e,       // 0 R>>\nen
      0x64, 0x6f, 0x62, 0x6a, 0x0a, 0x32, 0x20, 0x30,       // dobj\n2 0
      0x20, 0x6f, 0x62, 0x6a, 0x0a, 0x3c, 0x3c, 0x2f,       //  obj\n<</
      0x54, 0x79, 0x70, 0x65, 0x2f, 0x50, 0x61, 0x67,       // Type/Pag
      0x65, 0x73, 0x2f, 0x43, 0x6f, 0x75, 0x6e, 0x74,       // es/Count
      0x20, 0x31, 0x2f, 0x4b, 0x69, 0x64, 0x73, 0x5b,       //  1/Kids[
      0x33, 0x20, 0x30, 0x20, 0x52, 0x5d, 0x3e, 0x3e,       // 3 0 R]>>
      0x0a, 0x65, 0x6e, 0x64, 0x6f, 0x62, 0x6a, 0x0a,       // \nendobj\n
      0x33, 0x20, 0x30, 0x20, 0x6f, 0x62, 0x6a, 0x0a,       // 3 0 obj\n
      0x3c, 0x3c, 0x2f, 0x54, 0x79, 0x70, 0x65, 0x2f,       // <</Type/
      0x50, 0x61, 0x67, 0x65, 0x2f, 0x50, 0x61, 0x72,       // Page/Par
      0x65, 0x6e, 0x74, 0x20, 0x32, 0x20, 0x30, 0x20,       // ent 2 0
      0x52, 0x2f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x42,       // R/MediaB
      0x6f, 0x78, 0x5b, 0x30, 0x20, 0x30, 0x20, 0x36,       // ox[0 0 6
      0x31, 0x32, 0x20, 0x37, 0x39, 0x32, 0x5d, 0x3e,       // 12 792]>
      0x3e, 0x0a, 0x65, 0x6e, 0x64, 0x6f, 0x62, 0x6a,       // >\nendobj
      0x0a, 0x78, 0x72, 0x65, 0x66, 0x0a, 0x30, 0x20,       // \nxref\n0
      0x34, 0x0a, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,       // 4\n000000
      0x30, 0x30, 0x30, 0x30, 0x20, 0x36, 0x35, 0x35,       // 0000 655
      0x33, 0x35, 0x20, 0x66, 0x20, 0x0a, 0x74, 0x72,       // 35 f \ntr
      0x61, 0x69, 0x6c, 0x65, 0x72, 0x0a, 0x3c, 0x3c,       // ailer\n<<
      0x2f, 0x53, 0x69, 0x7a, 0x65, 0x20, 0x34, 0x2f,       // /Size 4/
      0x52, 0x6f, 0x6f, 0x74, 0x20, 0x31, 0x20, 0x30,       // Root 1 0
      0x20, 0x52, 0x3e, 0x3e, 0x0a, 0x73, 0x74, 0x61,       //  R>>\nsta
      0x72, 0x74, 0x78, 0x72, 0x65, 0x66, 0x0a, 0x32,       // rtxref\n2
      0x30, 0x30, 0x0a, 0x25, 0x25, 0x45, 0x4f, 0x46,       // 00\n%%EOF
      0x0a                                                   // \n
    ])
  }

  /**
   * Test PDF loading step
   */
  private async testPDFLoad(): Promise<boolean> {
    try {
      const startTime = performance.now()

      // Create test PDF file
      const pdfBytes = await this.createTestPDF()
      const testFile = new File([pdfBytes], 'test.pdf', { type: 'application/pdf' })
      
      const result = await this.service.loadPDF(testFile)
      
      this.testResults.performance.loadTime = performance.now() - startTime
      
      if (result.success && result.numPages > 0) {
        this.testResults.steps.pdfLoad = true
        return true
      } else {
        this.testResults.errors.push('PDF load failed: Invalid result')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`PDF load failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Test canvas rendering step
   */
  private async testCanvasRender(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      const pageData = await this.service.renderPageToCanvas(1)
      
      this.testResults.performance.renderTime = performance.now() - startTime
      
      if (pageData && pageData.rendered && pageData.canvas) {
        this.testResults.steps.canvasRender = true
        return true
      } else {
        this.testResults.errors.push('Canvas render failed: No page data')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`Canvas render failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Test edit operations
   */
  private async testEditOperations(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      // Test adding text element
      const textId = await this.service.addTextElement({
        x: 100,
        y: 100,
        text: 'Test Text',
        fontSize: 16,
        color: '#000000',
        fontFamily: 'Arial',
        pageNumber: 1
      })

      // Test adding highlight element
      const highlightId = await this.service.addHighlightElement({
        x: 200,
        y: 200,
        width: 100,
        height: 20,
        color: '#ffff00',
        pageNumber: 1
      })

      // Test adding comment element
      const commentId = await this.service.addCommentElement({
        x: 300,
        y: 300,
        text: 'Test Comment',
        pageNumber: 1
      })

      this.testResults.performance.editTime = performance.now() - startTime

      const elements = this.service.getEditElements()
      
      if (elements.length === 3 && textId && highlightId && commentId) {
        this.testResults.steps.editOperations = true
        return true
      } else {
        this.testResults.errors.push('Edit operations failed: Incorrect element count')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`Edit operations failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Test PDF generation step
   */
  private async testPDFGeneration(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      const pdfBytes = await this.service.generateModifiedPDF()
      
      this.testResults.performance.generateTime = performance.now() - startTime
      
      if (pdfBytes && pdfBytes.length > 0) {
        this.testResults.steps.pdfGeneration = true
        return true
      } else {
        this.testResults.errors.push('PDF generation failed: No output')
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.testResults.errors.push(`PDF generation failed: ${errorMessage}`)
      return false
    }
  }

  /**
   * Run complete workflow test
   */
  async runCompleteTest(): Promise<WorkflowTestResult> {
    console.log('Starting Canvas Workflow Test...')
    
    // Reset results
    this.testResults = {
      success: false,
      steps: {
        pdfLoad: false,
        canvasRender: false,
        editOperations: false,
        pdfGeneration: false
      },
      errors: [],
      performance: {
        loadTime: 0,
        renderTime: 0,
        editTime: 0,
        generateTime: 0
      }
    }

    // Run tests in sequence
    const pdfLoadSuccess = await this.testPDFLoad()
    console.log('PDF Load:', pdfLoadSuccess ? 'PASS' : 'FAIL')

    if (pdfLoadSuccess) {
      const canvasRenderSuccess = await this.testCanvasRender()
      console.log('Canvas Render:', canvasRenderSuccess ? 'PASS' : 'FAIL')

      if (canvasRenderSuccess) {
        const editOperationsSuccess = await this.testEditOperations()
        console.log('Edit Operations:', editOperationsSuccess ? 'PASS' : 'FAIL')

        if (editOperationsSuccess) {
          const pdfGenerationSuccess = await this.testPDFGeneration()
          console.log('PDF Generation:', pdfGenerationSuccess ? 'PASS' : 'FAIL')

          this.testResults.success = pdfGenerationSuccess
        }
      }
    }

    console.log('Canvas Workflow Test Complete:', this.testResults.success ? 'PASS' : 'FAIL')
    console.log('Performance:', this.testResults.performance)
    
    if (this.testResults.errors.length > 0) {
      console.log('Errors:', this.testResults.errors)
    }

    return this.testResults
  }

  /**
   * Get test results
   */
  getResults(): WorkflowTestResult {
    return { ...this.testResults }
  }
}

export default CanvasWorkflowTester
