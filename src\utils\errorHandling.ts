// Error handling utilities

export interface ErrorInfo {
  message: string
  stack?: string
  componentStack?: string
  timestamp: number
  userAgent: string
  url: string
}

export class ErrorLogger {
  private static instance: ErrorLogger
  private errors: ErrorInfo[] = []
  private maxErrors = 100

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger()
    }
    return ErrorLogger.instance
  }

  logError(error: Error, context?: string): void {
    const errorInfo: ErrorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // Add context if provided
    if (context) {
      errorInfo.message = `[${context}] ${errorInfo.message}`
    }

    this.errors.push(errorInfo)

    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorInfo)
    }
  }

  getErrors(): ErrorInfo[] {
    return [...this.errors]
  }

  clearErrors(): void {
    this.errors = []
  }
}

// Global error handler
export const setupGlobalErrorHandling = (): (() => void) => {
  const errorLogger = ErrorLogger.getInstance()

  // Handle unhandled promise rejections
  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    errorLogger.logError(
      new Error(`Unhandled promise rejection: ${event.reason}`),
      'Promise'
    )

    // Prevent the default browser behavior
    event.preventDefault()
  }

  // Handle global errors
  const handleGlobalError = (event: ErrorEvent) => {
    errorLogger.logError(
      new Error(`Global error: ${event.message} at ${event.filename}:${event.lineno}:${event.colno}`),
      'Global'
    )
  }

  window.addEventListener('unhandledrejection', handleUnhandledRejection)
  window.addEventListener('error', handleGlobalError)

  // Return cleanup function
  return () => {
    window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    window.removeEventListener('error', handleGlobalError)
  }

  // Handle React errors (this will be caught by ErrorBoundary)
  const originalConsoleError = console.error
  console.error = (...args) => {
    // Check if this is a React error
    const message = args.join(' ')
    if (message.includes('React') || message.includes('Warning:')) {
      // Filter out known harmless warnings
      if (
        message.includes('Warning: validateDOMNesting') ||
        message.includes('Warning: Each child in a list should have a unique "key" prop') ||
        message.includes('Warning: Failed prop type')
      ) {
        // Log these as warnings instead of errors
        if (process.env.NODE_ENV === 'development') {
          originalConsoleError.apply(console, args)
        }
        return
      }
    }
    
    // Log other errors normally
    originalConsoleError.apply(console, args)
  }
}

// Async error wrapper
export const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: string
): T => {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args)
    } catch (error) {
      const errorLogger = ErrorLogger.getInstance()
      errorLogger.logError(error as Error, context)
      throw error
    }
  }) as T
}

// Sync error wrapper
export const withSyncErrorHandling = <T extends (...args: any[]) => any>(
  fn: T,
  context?: string
): T => {
  return ((...args: Parameters<T>) => {
    try {
      return fn(...args)
    } catch (error) {
      const errorLogger = ErrorLogger.getInstance()
      errorLogger.logError(error as Error, context)
      throw error
    }
  }) as T
}

// Safe JSON parse
export const safeJsonParse = <T = any>(json: string, fallback: T): T => {
  try {
    return JSON.parse(json)
  } catch (error) {
    const errorLogger = ErrorLogger.getInstance()
    errorLogger.logError(error as Error, 'JSON Parse')
    return fallback
  }
}

// Safe localStorage operations
export const safeLocalStorage = {
  getItem: (key: string, fallback: string | null = null): string | null => {
    try {
      return localStorage.getItem(key)
    } catch (error) {
      const errorLogger = ErrorLogger.getInstance()
      errorLogger.logError(error as Error, 'LocalStorage Get')
      return fallback
    }
  },

  setItem: (key: string, value: string): boolean => {
    try {
      localStorage.setItem(key, value)
      return true
    } catch (error) {
      const errorLogger = ErrorLogger.getInstance()
      errorLogger.logError(error as Error, 'LocalStorage Set')
      return false
    }
  },

  removeItem: (key: string): boolean => {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      const errorLogger = ErrorLogger.getInstance()
      errorLogger.logError(error as Error, 'LocalStorage Remove')
      return false
    }
  }
}

// Validate environment
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check for required browser features
  if (!window.File) {
    errors.push('File API not supported')
  }

  if (!window.FileReader) {
    errors.push('FileReader API not supported')
  }

  if (!window.Blob) {
    errors.push('Blob API not supported')
  }

  if (!document.createElement('canvas').getContext) {
    errors.push('Canvas API not supported')
  }

  if (!window.localStorage) {
    errors.push('LocalStorage not supported')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
