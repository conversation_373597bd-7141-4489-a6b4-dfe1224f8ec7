/**
 * Workflow Test Panel Component
 * Provides UI for testing the canvas-based PDF workflow
 */

import React, { useState, useCallback } from 'react'
import { Play, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react'
import CanvasWorkflowTester, { type WorkflowTestResult } from '../utils/canvasWorkflowTest'

interface WorkflowTestPanelProps {
  isVisible: boolean
  onClose: () => void
}

const WorkflowTestPanel: React.FC<WorkflowTestPanelProps> = ({ isVisible, onClose }) => {
  const [isRunning, setIsRunning] = useState(false)
  const [testResults, setTestResults] = useState<WorkflowTestResult | null>(null)
  const [tester] = useState(() => new CanvasWorkflowTester())

  const runTest = useCallback(async () => {
    setIsRunning(true)
    setTestResults(null)
    
    try {
      const results = await tester.runCompleteTest()
      setTestResults(results)
    } catch (error) {
      console.error('Test execution failed:', error)
      setTestResults({
        success: false,
        steps: {
          pdfLoad: false,
          canvasRender: false,
          editOperations: false,
          pdfGeneration: false
        },
        errors: [`Test execution failed: ${error}`],
        performance: {
          loadTime: 0,
          renderTime: 0,
          editTime: 0,
          generateTime: 0
        }
      })
    } finally {
      setIsRunning(false)
    }
  }, [tester])

  const getStepIcon = (success: boolean) => {
    return success ? (
      <CheckCircle size={16} className="text-green-500" />
    ) : (
      <XCircle size={16} className="text-red-500" />
    )
  }

  const formatTime = (ms: number) => {
    return `${ms.toFixed(1)}ms`
  }

  if (!isVisible) return null

  return (
    <div className="workflow-test-panel">
      <div className="test-panel-overlay" onClick={onClose} />
      <div className="test-panel-content">
        <div className="test-panel-header">
          <h3>Canvas Workflow Test</h3>
          <button onClick={onClose} className="close-button">×</button>
        </div>

        <div className="test-panel-body">
          <div className="test-description">
            <p>This test validates the complete PDF → Canvas → Edit → PDF workflow:</p>
            <ul>
              <li>Load PDF file and initialize service</li>
              <li>Render PDF pages to canvas</li>
              <li>Perform edit operations (text, highlight, comment)</li>
              <li>Generate modified PDF with edits</li>
            </ul>
          </div>

          <div className="test-controls">
            <button
              onClick={runTest}
              disabled={isRunning}
              className="run-test-button"
            >
              {isRunning ? (
                <>
                  <Clock size={16} className="animate-spin" />
                  Running Test...
                </>
              ) : (
                <>
                  <Play size={16} />
                  Run Workflow Test
                </>
              )}
            </button>
          </div>

          {testResults && (
            <div className="test-results">
              <div className={`test-summary ${testResults.success ? 'success' : 'failure'}`}>
                {testResults.success ? (
                  <>
                    <CheckCircle size={20} className="text-green-500" />
                    <span>All tests passed! Canvas workflow is working correctly.</span>
                  </>
                ) : (
                  <>
                    <AlertTriangle size={20} className="text-red-500" />
                    <span>Some tests failed. Check details below.</span>
                  </>
                )}
              </div>

              <div className="test-steps">
                <h4>Test Steps</h4>
                <div className="step-list">
                  <div className="test-step">
                    {getStepIcon(testResults.steps.pdfLoad)}
                    <span>PDF Load</span>
                    <span className="step-time">{formatTime(testResults.performance.loadTime)}</span>
                  </div>
                  <div className="test-step">
                    {getStepIcon(testResults.steps.canvasRender)}
                    <span>Canvas Render</span>
                    <span className="step-time">{formatTime(testResults.performance.renderTime)}</span>
                  </div>
                  <div className="test-step">
                    {getStepIcon(testResults.steps.editOperations)}
                    <span>Edit Operations</span>
                    <span className="step-time">{formatTime(testResults.performance.editTime)}</span>
                  </div>
                  <div className="test-step">
                    {getStepIcon(testResults.steps.pdfGeneration)}
                    <span>PDF Generation</span>
                    <span className="step-time">{formatTime(testResults.performance.generateTime)}</span>
                  </div>
                </div>
              </div>

              <div className="performance-summary">
                <h4>Performance Summary</h4>
                <div className="performance-stats">
                  <div className="stat">
                    <span>Total Time:</span>
                    <span>{formatTime(
                      testResults.performance.loadTime +
                      testResults.performance.renderTime +
                      testResults.performance.editTime +
                      testResults.performance.generateTime
                    )}</span>
                  </div>
                  <div className="stat">
                    <span>Average Step Time:</span>
                    <span>{formatTime(
                      (testResults.performance.loadTime +
                       testResults.performance.renderTime +
                       testResults.performance.editTime +
                       testResults.performance.generateTime) / 4
                    )}</span>
                  </div>
                </div>
              </div>

              {testResults.errors.length > 0 && (
                <div className="test-errors">
                  <h4>Errors</h4>
                  <div className="error-list">
                    {testResults.errors.map((error, index) => (
                      <div key={index} className="error-item">
                        <XCircle size={14} className="text-red-500" />
                        <span>{error}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default WorkflowTestPanel
