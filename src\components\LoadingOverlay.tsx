import React from 'react'
import { Loader2 } from 'lucide-react'

interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
  progress?: number
  onCancel?: () => void
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = 'Loading...',
  progress,
  onCancel
}) => {
  if (!isVisible) return null

  return (
    <div className="loading-overlay-backdrop">
      <div className="loading-overlay-content">
        <div className="loading-spinner-large">
          <Loader2 size={48} className="animate-spin" />
        </div>
        
        <h3 className="loading-title">{message}</h3>
        
        {progress !== undefined && (
          <div className="loading-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
              />
            </div>
            <span className="progress-text">{Math.round(progress)}%</span>
          </div>
        )}
        
        {onCancel && (
          <button onClick={onCancel} className="loading-cancel-button">
            Cancel
          </button>
        )}
      </div>
    </div>
  )
}

export default LoadingOverlay
