import { useState, useEffect } from 'react'
import './App.css'
import FileUpload from './components/FileUpload'
import PDFEditor from './components/PDFEditor'
import ErrorBoundary from './components/ErrorBoundary'
import LoadingOverlay from './components/LoadingOverlay'
import WorkflowTestPanel from './components/WorkflowTestPanel'
import { ToastContainer, useToast } from './components/Toast'

// Define utility functions locally to avoid import issues
const setupGlobalErrorHandling = () => {
  const handleError = (event: ErrorEvent) => {
    console.error('Global error:', event.error)
  }

  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    console.error('Unhandled promise rejection:', event.reason)
  }

  window.addEventListener('error', handleError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)

  return () => {
    window.removeEventListener('error', handleError)
    window.removeEventListener('unhandledrejection', handleUnhandledRejection)
  }
}

const validateEnvironment = () => {
  const errors: string[] = []

  if (!window.File) errors.push('File API')
  if (!window.FileReader) errors.push('FileReader API')
  if (!window.ArrayBuffer) errors.push('ArrayBuffer')

  return {
    isValid: errors.length === 0,
    errors
  }
}

const setupConsoleFilters = () => {
  // Simple console filter setup
  if (process.env.NODE_ENV === 'development') {
    console.log('Console filters enabled')
  }
}

const startAutoReporting = (intervalMinutes: number) => {
  const interval = setInterval(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Auto-reporting check')
    }
  }, intervalMinutes * 60 * 1000)

  return () => clearInterval(interval)
}

const quickConsoleHealthCheck = () => {
  return {
    isHealthy: true,
    timestamp: Date.now()
  }
}

function App() {
  const [pdfFile, setPdfFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')
  const [showTestPanel, setShowTestPanel] = useState(false)
  const { toasts, addToast, removeToast } = useToast()

  // Setup global error handling and environment validation
  useEffect(() => {
    const cleanupErrorHandling = setupGlobalErrorHandling()
    setupConsoleFilters()

    // Start auto-reporting in development (every 10 minutes)
    const stopAutoReporting = startAutoReporting(10)

    const { isValid, errors } = validateEnvironment()
    if (!isValid) {
      addToast({
        type: 'error',
        title: 'Browser Compatibility Issue',
        message: `Your browser is missing required features: ${errors.join(', ')}`,
        duration: 10000
      })
    }

    // Run initial console health check in development
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        const healthResult = quickConsoleHealthCheck()
        if (!healthResult.isHealthy) {
          console.warn('Console Health Check:', healthResult)
        }
      }, 2000) // Wait 2 seconds for initial load
    }

    // Cleanup function
    return () => {
      cleanupErrorHandling()
      stopAutoReporting()
    }
  }, [addToast])

  const handleFileSelect = async (file: File) => {
    try {
      setIsLoading(true)
      setLoadingMessage('Processing PDF file...')

      // Simulate file processing time
      await new Promise(resolve => setTimeout(resolve, 1000))

      setPdfFile(file)
      addToast({
        type: 'success',
        title: 'PDF Loaded Successfully',
        message: `${file.name} is ready for editing`,
        duration: 3000
      })
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Failed to Load PDF',
        message: 'Please try again with a different file',
        duration: 5000
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToUpload = () => {
    setPdfFile(null)
    addToast({
      type: 'info',
      title: 'Returned to Upload',
      message: 'You can now select a new PDF file',
      duration: 2000
    })
  }

  return (
    <ErrorBoundary>
      <div className="app">
        <header className="app-header" role="banner">
          <div className="header-content">
            <h1 itemProp="name">Open PDF Editor - Free Online PDF Editor</h1>
            <p className="header-subtitle" itemProp="description">
              Edit PDFs instantly in your browser. No downloads, no registration, completely free.
            </p>
            {process.env.NODE_ENV === 'development' && (
              <button
                onClick={() => setShowTestPanel(true)}
                className="test-panel-button"
                style={{
                  position: 'absolute',
                  top: '10px',
                  right: '10px',
                  padding: '8px 16px',
                  background: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                Test Workflow
              </button>
            )}
          </div>
        </header>

        <main id="main-content" className="app-main">
          {!pdfFile ? (
            <FileUpload onFileSelect={handleFileSelect} />
          ) : (
            <PDFEditor
              file={pdfFile}
              onBack={handleBackToUpload}
            />
          )}
        </main>

        <LoadingOverlay
          isVisible={isLoading}
          message={loadingMessage}
        />

        <ToastContainer
          toasts={toasts}
          onRemove={removeToast}
        />

        <WorkflowTestPanel
          isVisible={showTestPanel}
          onClose={() => setShowTestPanel(false)}
        />
      </div>
    </ErrorBoundary>
  )
}

export default App
