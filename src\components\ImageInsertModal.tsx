import React, { useState, useRef, useCallback, useEffect } from 'react'
import { X, Upload, Image as ImageIcon, RotateCw, Maximize2 } from 'lucide-react'

interface ImageInsertModalProps {
  isOpen: boolean
  x: number
  y: number
  onSave: (imageData: string, x: number, y: number, width: number, height: number, rotation: number) => void
  onCancel: () => void
}

const ImageInsertModal: React.FC<ImageInsertModalProps> = ({
  isOpen,
  x,
  y,
  onSave,
  onCancel
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [imagePosition, setImagePosition] = useState({ x, y })
  const [imageSize, setImageSize] = useState({ width: 200, height: 150 })
  const [rotation, setRotation] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Cleanup object URLs when component unmounts or image changes
  useEffect(() => {
    return () => {
      if (selectedImage && selectedImage.startsWith('blob:')) {
        URL.revokeObjectURL(selectedImage)
      }
    }
  }, [selectedImage])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Image file must be less than 10MB')
      return
    }



    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setSelectedImage(result)
      
      // Get image dimensions to set initial size
      const img = new Image()
      img.onload = () => {
        const aspectRatio = img.width / img.height
        const maxWidth = 300
        const maxHeight = 200
        
        let width = Math.min(img.width, maxWidth)
        let height = width / aspectRatio
        
        if (height > maxHeight) {
          height = maxHeight
          width = height * aspectRatio
        }
        
        setImageSize({ width, height })
      }
      img.src = result
    }
    reader.readAsDataURL(file)
  }, [])

  const handleDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDrag = useCallback((e: React.MouseEvent) => {
    if (!isDragging) return
    
    const rect = e.currentTarget.getBoundingClientRect()
    const newX = e.clientX - rect.left
    const newY = e.clientY - rect.top
    
    setImagePosition({ x: newX, y: newY })
  }, [isDragging])

  const handleDragEnd = useCallback(() => {
    setIsDragging(false)
  }, [])

  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
  }, [])

  const handleResize = useCallback((e: React.MouseEvent) => {
    if (!isResizing) return
    
    const rect = e.currentTarget.getBoundingClientRect()
    const newWidth = Math.max(50, e.clientX - rect.left - imagePosition.x)
    const newHeight = Math.max(50, e.clientY - rect.top - imagePosition.y)
    
    setImageSize({ width: newWidth, height: newHeight })
  }, [isResizing, imagePosition])

  const handleResizeEnd = useCallback(() => {
    setIsResizing(false)
  }, [])

  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360)
  }, [])

  const handleSave = useCallback(() => {
    if (!selectedImage) return
    
    onSave(
      selectedImage,
      imagePosition.x,
      imagePosition.y,
      imageSize.width,
      imageSize.height,
      rotation
    )
  }, [selectedImage, imagePosition, imageSize, rotation, onSave])

  const handleReset = useCallback(() => {
    setImagePosition({ x, y })
    setImageSize({ width: 200, height: 150 })
    setRotation(0)
  }, [x, y])

  if (!isOpen) return null

  return (
    <>
      <div className="modal-overlay" onClick={onCancel} />
      <div className="image-insert-modal">
        <div className="modal-header">
          <div className="header-icon">
            <ImageIcon size={20} />
            <h3>Insert Image</h3>
          </div>
          <button onClick={onCancel} className="close-button">
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {!selectedImage ? (
            <div className="image-upload-area">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="file-input"
                id="image-input"
              />
              <label htmlFor="image-input" className="upload-label">
                <Upload size={48} />
                <h4>Select Image</h4>
                <p>Click to browse or drag and drop</p>
                <p className="file-info">Supported: JPG, PNG, GIF (max 10MB)</p>
              </label>
            </div>
          ) : (
            <div className="image-preview-area">
              <div 
                className="image-preview-container"
                onMouseMove={isDragging ? handleDrag : isResizing ? handleResize : undefined}
                onMouseUp={isDragging ? handleDragEnd : isResizing ? handleResizeEnd : undefined}
              >
                <div
                  className="image-preview"
                  style={{
                    left: imagePosition.x,
                    top: imagePosition.y,
                    width: imageSize.width,
                    height: imageSize.height,
                    transform: `rotate(${rotation}deg)`,
                    cursor: isDragging ? 'grabbing' : 'grab'
                  }}
                  onMouseDown={handleDragStart}
                >
                  <img src={selectedImage} alt="Preview" />
                  <div
                    className="resize-handle"
                    onMouseDown={handleResizeStart}
                  >
                    <Maximize2 size={12} />
                  </div>
                </div>
              </div>

              <div className="image-controls">
                <div className="control-group">
                  <label>Position:</label>
                  <div className="position-inputs">
                    <input
                      type="number"
                      value={Math.round(imagePosition.x)}
                      onChange={(e) => setImagePosition(prev => ({ ...prev, x: parseInt(e.target.value) || 0 }))}
                      placeholder="X"
                    />
                    <input
                      type="number"
                      value={Math.round(imagePosition.y)}
                      onChange={(e) => setImagePosition(prev => ({ ...prev, y: parseInt(e.target.value) || 0 }))}
                      placeholder="Y"
                    />
                  </div>
                </div>

                <div className="control-group">
                  <label>Size:</label>
                  <div className="size-inputs">
                    <input
                      type="number"
                      value={Math.round(imageSize.width)}
                      onChange={(e) => setImageSize(prev => ({ ...prev, width: parseInt(e.target.value) || 50 }))}
                      placeholder="Width"
                    />
                    <input
                      type="number"
                      value={Math.round(imageSize.height)}
                      onChange={(e) => setImageSize(prev => ({ ...prev, height: parseInt(e.target.value) || 50 }))}
                      placeholder="Height"
                    />
                  </div>
                </div>

                <div className="control-group">
                  <label>Rotation: {rotation}°</label>
                  <button onClick={handleRotate} className="rotate-button">
                    <RotateCw size={16} />
                    Rotate 90°
                  </button>
                </div>

                <div className="control-actions">
                  <button onClick={handleReset} className="reset-button">
                    Reset
                  </button>
                  <button onClick={() => setSelectedImage(null)} className="change-button">
                    Change Image
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button onClick={onCancel} className="cancel-button">
            Cancel
          </button>
          {selectedImage && (
            <button onClick={handleSave} className="save-button">
              Insert Image
            </button>
          )}
        </div>
      </div>
    </>
  )
}

export default ImageInsertModal
