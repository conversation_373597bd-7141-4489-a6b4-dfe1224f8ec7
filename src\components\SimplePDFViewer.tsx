import React, { useState, useEffect, useCallback, useRef } from 'react'
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, Download, AlertTriangle, ExternalLink, Shield, Info } from 'lucide-react'
import BrowserCompatibilityManager, { type CompatibilityResult } from '../utils/browserCompatibility'

interface SimplePDFViewerProps {
  file: File
  onPageClick?: (pageNumber: number, x: number, y: number) => void
}

type ViewerMode = 'iframe' | 'embed' | 'object' | 'download' | 'error'

const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({ file, onPageClick }) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [scale, setScale] = useState<number>(1.0)
  const [viewerMode, setViewerMode] = useState<ViewerMode>('iframe')
  const [debugInfo, setDebugInfo] = useState<string[]>([])
  const [compatibilityResult, setCompatibilityResult] = useState<CompatibilityResult | null>(null)
  const [showCompatibilityInfo, setShowCompatibilityInfo] = useState<boolean>(false)
  const [browserInstructions, setBrowserInstructions] = useState<string[]>([])

  const iframeRef = useRef<HTMLIFrameElement>(null)
  const embedRef = useRef<HTMLEmbedElement>(null)
  const objectRef = useRef<HTMLObjectElement>(null)
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const compatibilityManager = BrowserCompatibilityManager.getInstance()

  // Add debug log helper
  const addDebugLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    const logMessage = `[${timestamp}] ${message}`
    console.log(`SimplePDFViewer: ${logMessage}`)
    setDebugInfo(prev => [...prev.slice(-9), logMessage]) // Keep last 10 logs
  }, [])

  // Detect browser capabilities
  const detectBrowserCapabilities = useCallback(() => {
    const capabilities = {
      userAgent: navigator.userAgent,
      pdfSupport: 'application/pdf' in navigator.mimeTypes,
      blobSupport: typeof URL.createObjectURL === 'function',
      iframeSupport: true, // Assume true, will test
      embedSupport: true,  // Assume true, will test
    }

    addDebugLog(`Browser capabilities: ${JSON.stringify(capabilities)}`)
    return capabilities
  }, [addDebugLog])

  // Test blob URL accessibility using multiple methods
  const testBlobUrlAccess = useCallback(async (url: string): Promise<boolean> => {
    try {
      addDebugLog(`Testing blob URL accessibility: ${url.substring(0, 50)}...`)

      // Method 1: Try creating an image element (works for most blob URLs)
      return new Promise((resolve) => {
        const img = new Image()
        const timeout = setTimeout(() => {
          addDebugLog('Blob URL test timeout - assuming accessible')
          resolve(true) // Assume accessible if we can't test
        }, 2000)

        img.onload = () => {
          clearTimeout(timeout)
          addDebugLog('Blob URL accessible via image test')
          resolve(true)
        }

        img.onerror = () => {
          clearTimeout(timeout)
          addDebugLog('Blob URL not accessible via image test, trying fetch...')

          // Method 2: Try fetch as fallback
          fetch(url, { method: 'HEAD' })
            .then(response => {
              const accessible = response.ok
              addDebugLog(`Blob URL fetch test result: ${accessible}`)
              resolve(accessible)
            })
            .catch(error => {
              addDebugLog(`Blob URL fetch test failed: ${error.message}`)
              // If fetch fails, assume the URL is still valid for iframe use
              resolve(true)
            })
        }

        // Set a data URL to trigger load/error
        img.src = url
      })
    } catch (error) {
      addDebugLog(`Blob URL test failed: ${error}`)
      // If testing fails, assume the URL is valid
      return true
    }
  }, [addDebugLog])

  // Initialize viewer with comprehensive browser compatibility analysis
  useEffect(() => {
    if (!file) return

    const initializeViewer = async () => {
      addDebugLog(`Initializing viewer for file: ${file.name} (${file.size} bytes, ${file.type})`)

      if (file.type !== 'application/pdf') {
        setError('Invalid file type. Please select a PDF file.')
        setIsLoading(false)
        setViewerMode('error')
        return
      }

      try {
        // Perform comprehensive browser compatibility analysis
        addDebugLog('Analyzing browser compatibility...')
        const compatibility = await compatibilityManager.analyzeCompatibility(file)
        setCompatibilityResult(compatibility)

        addDebugLog(`Browser: ${compatibility.browserInfo.name} ${compatibility.browserInfo.version}`)
        addDebugLog(`Privacy focused: ${compatibility.browserInfo.isPrivacyFocused}`)
        addDebugLog(`Blob iframe support: ${compatibility.canUseBlobIframes}`)

        // Get browser-specific instructions
        const instructions = compatibilityManager.getBrowserInstructions(compatibility.browserInfo.name)
        setBrowserInstructions(instructions)

        // Create object URL
        addDebugLog('Creating object URL...')
        const url = URL.createObjectURL(file)
        addDebugLog(`Object URL created: ${url}`)

        // Determine best viewer mode based on compatibility
        let initialMode: ViewerMode = 'download'

        if (compatibility.canUseBlobIframes) {
          initialMode = 'iframe'
          addDebugLog('Using iframe mode (blob URLs supported)')
        } else if (compatibility.canUseEmbedElements) {
          initialMode = 'embed'
          addDebugLog('Using embed mode (iframe blocked, embed available)')
        } else if (compatibility.canUseObjectElements) {
          initialMode = 'object'
          addDebugLog('Using object mode (iframe/embed blocked, object available)')
        } else {
          addDebugLog('No viewer modes available, using download mode')
          setError(`${compatibility.browserInfo.name} blocks PDF viewing. Please download the file or try a different browser.`)
        }

        setFileUrl(url)
        setViewerMode(initialMode)
        setIsLoading(false)
        setError(null)

        // Show compatibility info if there are issues
        if (compatibility.recommendations.length > 0) {
          setShowCompatibilityInfo(true)
        }

        return () => {
          addDebugLog(`Cleaning up object URL: ${url}`)
          URL.revokeObjectURL(url)
        }
      } catch (error) {
        addDebugLog(`Error during initialization: ${error}`)
        setError('Failed to process PDF file')
        setViewerMode('download')
        setIsLoading(false)
      }
    }

    initializeViewer()
  }, [file, addDebugLog])

  // Handle iframe load events
  const handleIframeLoad = useCallback(() => {
    addDebugLog('Iframe loaded successfully')
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current)
      loadTimeoutRef.current = null
    }
    setIsLoading(false)
    setError(null)
  }, [addDebugLog])

  const handleIframeError = useCallback(() => {
    addDebugLog('Iframe failed to load, trying embed fallback')
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current)
      loadTimeoutRef.current = null
    }
    setViewerMode('embed')
  }, [addDebugLog])

  const handleEmbedError = useCallback(() => {
    addDebugLog('Embed failed to load, trying object fallback')
    setViewerMode('object')
  }, [addDebugLog])

  const handleObjectError = useCallback(() => {
    addDebugLog('Object failed to load, switching to download mode')
    setViewerMode('download')
    setError('PDF cannot be displayed in browser. Please download to view.')
    setIsLoading(false)
  }, [addDebugLog])

  const handleIframeClick = useCallback((event: React.MouseEvent<HTMLIFrameElement>) => {
    if (onPageClick) {
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      onPageClick(1, x, y) // Simple implementation assumes page 1
    }
  }, [onPageClick])

  // Set up load timeout
  useEffect(() => {
    if (viewerMode === 'iframe' && fileUrl) {
      addDebugLog('Setting up iframe load timeout (10 seconds)')
      loadTimeoutRef.current = setTimeout(() => {
        addDebugLog('Iframe load timeout, trying embed fallback')
        setViewerMode('embed')
      }, 10000)

      return () => {
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current)
          loadTimeoutRef.current = null
        }
      }
    }
  }, [viewerMode, fileUrl, addDebugLog])

  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(3.0, prev + 0.2))
  }, [])

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(0.5, prev - 0.2))
  }, [])

  const handleDownload = useCallback(() => {
    if (fileUrl) {
      const a = document.createElement('a')
      a.href = fileUrl
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }, [fileUrl, file.name])

  // Render loading state
  if (isLoading) {
    return (
      <div className="pdf-viewer-loading">
        <div className="loading-spinner"></div>
        <p>Loading PDF...</p>
        <div className="debug-info">
          {debugInfo.map((log, index) => (
            <div key={index} className="debug-log">{log}</div>
          ))}
        </div>
      </div>
    )
  }

  // Render download-only mode
  if (viewerMode === 'download' || (!fileUrl && !error)) {
    return (
      <div className="pdf-download-mode">
        <div className="download-content">
          <AlertTriangle size={48} className="warning-icon" />
          <h3>PDF Viewing Not Available</h3>
          <p>Your browser cannot display this PDF inline. Please download it to view.</p>

          <div className="file-info">
            <p><strong>File:</strong> {file.name}</p>
            <p><strong>Size:</strong> {(file.size / 1024).toFixed(1)} KB</p>
            <p><strong>Type:</strong> {file.type}</p>
          </div>

          <div className="download-actions">
            <button onClick={handleDownload} className="download-button">
              <Download size={20} />
              Download PDF
            </button>
            <button
              onClick={() => window.open(fileUrl || URL.createObjectURL(file), '_blank')}
              className="open-button"
            >
              <ExternalLink size={20} />
              Open in New Tab
            </button>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <details className="debug-details">
              <summary>Debug Information</summary>
              <div className="debug-info">
                {debugInfo.map((log, index) => (
                  <div key={index} className="debug-log">{log}</div>
                ))}
              </div>
            </details>
          )}
        </div>
      </div>
    )
  }

  // Render error state
  if (viewerMode === 'error' || error) {
    return (
      <div className="pdf-viewer-error">
        <AlertTriangle size={48} className="error-icon" />
        <h3>PDF Viewing Error</h3>
        <p>{error || 'An unexpected error occurred while loading the PDF.'}</p>

        <div className="error-actions">
          <button onClick={handleDownload} className="download-button">
            <Download size={20} />
            Download PDF
          </button>
          <button
            onClick={() => {
              setError(null)
              setIsLoading(true)
              setViewerMode('iframe')
            }}
            className="retry-button"
          >
            Retry
          </button>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="debug-details">
            <summary>Debug Information</summary>
            <div className="debug-info">
              {debugInfo.map((log, index) => (
                <div key={index} className="debug-log">{log}</div>
              ))}
            </div>
          </details>
        )}
      </div>
    )
  }

  // Render main viewer
  return (
    <div className="pdf-viewer-container">
      <div className="pdf-controls">
        <div className="zoom-controls">
          <button onClick={zoomOut} className="control-button">
            <ZoomOut size={20} />
          </button>

          <span className="zoom-info">
            {Math.round(scale * 100)}%
          </span>

          <button onClick={zoomIn} className="control-button">
            <ZoomIn size={20} />
          </button>
        </div>

        <div className="pdf-info">
          <span>Reliable PDF Viewer ({viewerMode})</span>
          {compatibilityResult && (
            <span className="browser-info">
              {compatibilityResult.browserInfo.name} {compatibilityResult.browserInfo.version}
            </span>
          )}
        </div>

        <div className="compatibility-controls">
          {compatibilityResult && compatibilityResult.recommendations.length > 0 && (
            <button
              onClick={() => setShowCompatibilityInfo(!showCompatibilityInfo)}
              className={`control-button ${showCompatibilityInfo ? 'active' : ''}`}
              title="Browser Compatibility Info"
            >
              <Shield size={16} />
              {compatibilityResult.browserInfo.isPrivacyFocused && <AlertTriangle size={12} />}
            </button>
          )}
          <button
            onClick={() => setShowCompatibilityInfo(!showCompatibilityInfo)}
            className="control-button"
            title="Show Debug Info"
          >
            <Info size={16} />
          </button>
        </div>

        <div className="viewer-mode-controls">
          <button
            onClick={() => setViewerMode('iframe')}
            className={`control-button ${viewerMode === 'iframe' ? 'active' : ''}`}
            title="Iframe Mode"
            disabled={compatibilityResult && !compatibilityResult.canUseBlobIframes}
          >
            Iframe
          </button>
          <button
            onClick={() => setViewerMode('embed')}
            className={`control-button ${viewerMode === 'embed' ? 'active' : ''}`}
            title="Embed Mode"
            disabled={compatibilityResult && !compatibilityResult.canUseEmbedElements}
          >
            Embed
          </button>
          <button
            onClick={() => setViewerMode('object')}
            className={`control-button ${viewerMode === 'object' ? 'active' : ''}`}
            title="Object Mode"
            disabled={compatibilityResult && !compatibilityResult.canUseObjectElements}
          >
            Object
          </button>
          <button
            onClick={() => setViewerMode('download')}
            className={`control-button ${viewerMode === 'download' ? 'active' : ''}`}
            title="Download Mode"
          >
            Download
          </button>
        </div>

        <div className="download-controls">
          <button onClick={handleDownload} className="control-button" title="Download PDF">
            <Download size={20} />
          </button>
        </div>
      </div>

      <div className="pdf-document-container">
        <div
          className="simple-pdf-container"
          style={{
            transform: `scale(${scale})`,
            transformOrigin: 'top left',
            width: `${100 / scale}%`,
            height: `${100 / scale}%`
          }}
        >
          {viewerMode === 'iframe' && fileUrl && (
            <iframe
              ref={iframeRef}
              src={fileUrl}
              width="100%"
              height="100%"
              style={{
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                minHeight: '600px'
              }}
              title="PDF Viewer (Iframe)"
              onClick={handleIframeClick}
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              sandbox="allow-same-origin allow-scripts allow-forms allow-downloads"
            />
          )}

          {viewerMode === 'embed' && fileUrl && (
            <embed
              ref={embedRef}
              src={fileUrl}
              type="application/pdf"
              width="100%"
              height="600px"
              style={{
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              title="PDF Viewer (Embed)"
              onError={handleEmbedError}
            />
          )}

          {viewerMode === 'object' && fileUrl && (
            <object
              ref={objectRef}
              data={fileUrl}
              type="application/pdf"
              width="100%"
              height="600px"
              style={{
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              title="PDF Viewer (Object)"
              onError={handleObjectError}
            >
              <p>Your browser does not support PDF viewing. <a href={fileUrl} download={file.name}>Download the PDF</a></p>
            </object>
          )}
        </div>

        {/* Compatibility Information Panel */}
        {showCompatibilityInfo && compatibilityResult && (
          <div className="compatibility-panel">
            <div className="compatibility-header">
              <h3>Browser Compatibility Information</h3>
              <button
                onClick={() => setShowCompatibilityInfo(false)}
                className="close-button"
              >
                ×
              </button>
            </div>

            <div className="browser-details">
              <h4>Browser: {compatibilityResult.browserInfo.name} {compatibilityResult.browserInfo.version}</h4>
              <div className="capability-grid">
                <div className={`capability ${compatibilityResult.browserInfo.supportsBlob ? 'supported' : 'not-supported'}`}>
                  Blob URLs: {compatibilityResult.browserInfo.supportsBlob ? '✅' : '❌'}
                </div>
                <div className={`capability ${compatibilityResult.browserInfo.supportsPDFViewing ? 'supported' : 'not-supported'}`}>
                  PDF Viewing: {compatibilityResult.browserInfo.supportsPDFViewing ? '✅' : '❌'}
                </div>
                <div className={`capability ${compatibilityResult.canUseBlobIframes ? 'supported' : 'not-supported'}`}>
                  Iframe PDFs: {compatibilityResult.canUseBlobIframes ? '✅' : '❌'}
                </div>
                <div className={`capability ${compatibilityResult.canUseEmbedElements ? 'supported' : 'not-supported'}`}>
                  Embed Elements: {compatibilityResult.canUseEmbedElements ? '✅' : '❌'}
                </div>
              </div>
            </div>

            {compatibilityResult.recommendations.length > 0 && (
              <div className="recommendations">
                <h4>Recommendations:</h4>
                <ul>
                  {compatibilityResult.recommendations.map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}

            {browserInstructions.length > 0 && (
              <div className="browser-instructions">
                <h4>How to Enable PDF Viewing in {compatibilityResult.browserInfo.name}:</h4>
                <ol>
                  {browserInstructions.map((instruction, index) => (
                    <li key={index}>{instruction}</li>
                  ))}
                </ol>
              </div>
            )}

            {compatibilityResult.fallbackMethods.length > 0 && (
              <div className="fallback-methods">
                <h4>Available Fallback Methods:</h4>
                <div className="fallback-buttons">
                  {compatibilityResult.fallbackMethods.map((method, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        switch (method) {
                          case 'embed-element':
                            setViewerMode('embed')
                            break
                          case 'object-element':
                            setViewerMode('object')
                            break
                          case 'direct-download':
                            setViewerMode('download')
                            break
                        }
                      }}
                      className="fallback-button"
                    >
                      Try {method.replace('-', ' ')}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {process.env.NODE_ENV === 'development' && (
          <div className="debug-panel">
            <details>
              <summary>Debug Information ({debugInfo.length} logs)</summary>
              <div className="debug-info">
                {debugInfo.map((log, index) => (
                  <div key={index} className="debug-log">{log}</div>
                ))}
              </div>
            </details>
          </div>
        )}
      </div>
    </div>
  )
}

export default SimplePDFViewer
