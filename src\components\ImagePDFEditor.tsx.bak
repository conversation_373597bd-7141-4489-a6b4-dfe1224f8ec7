import React, { useState, useEffect, useCallback, useRef } from 'react'
import { ZoomIn, ZoomOut, RotateCw, Download, Save, Type, Highlighter, MessageSquare, Image as ImageIcon, Pen, Eraser } from 'lucide-react'
import PDFToImageService, { type PDFPageImage, type ConversionProgress } from '../services/pdfToImageService'

interface ImagePDFEditorProps {
  file: File
  onBack: () => void
}

interface EditElement {
  id: string
  type: 'text' | 'highlight' | 'comment' | 'drawing' | 'image'
  pageNumber: number
  x: number
  y: number
  width?: number
  height?: number
  content?: string
  style?: {
    fontSize?: number
    color?: string
    fontFamily?: string
    backgroundColor?: string
    strokeWidth?: number
  }
}

type ToolType = 'select' | 'text' | 'highlight' | 'comment' | 'drawing' | 'image' | 'eraser'

const ImagePDFEditor: React.FC<ImagePDFEditorProps> = ({ file, onBack }) => {
  const [pdfImages, setPdfImages] = useState<PDFPageImage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [conversionProgress, setConversionProgress] = useState<ConversionProgress | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [scale, setScale] = useState(1.0)
  const [activeTool, setActiveTool] = useState<ToolType>('select')
  const [editElements, setEditElements] = useState<EditElement[]>([])
  const [isDrawing, setIsDrawing] = useState(false)
  const [drawingPath, setDrawingPath] = useState<{x: number, y: number}[]>([])
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const pdfToImageService = PDFToImageService.getInstance()

  // Convert PDF to images on component mount
  useEffect(() => {
    const convertPDF = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const images = await pdfToImageService.convertPDFToImages(
          file,
          { scale: 2.0, format: 'png', quality: 0.9 },
          (progress) => {
            setConversionProgress(progress)
          }
        )

        if (images.length === 0) {
          throw new Error('Failed to convert PDF to images')
        }

        setPdfImages(images)
        setCurrentPage(1)
        setIsLoading(false)
      } catch (err) {
        console.error('PDF conversion error:', err)
        setError('Failed to convert PDF for editing. Please try a different file.')
        setIsLoading(false)
      }
    }

    convertPDF()
  }, [file])

  // Handle canvas click for adding elements
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || activeTool === 'select') return

    const rect = canvasRef.current.getBoundingClientRect()
    const x = (event.clientX - rect.left) / scale
    const y = (event.clientY - rect.top) / scale

    const newElement: EditElement = {
      id: Date.now().toString(),
      type: activeTool as any,
      pageNumber: currentPage,
      x,
      y,
      content: activeTool === 'text' ? 'New Text' : activeTool === 'comment' ? 'New Comment' : undefined,
      style: {
        fontSize: 16,
        color: activeTool === 'highlight' ? '#ffff00' : '#000000',
        backgroundColor: activeTool === 'highlight' ? '#ffff0080' : undefined,
        strokeWidth: 2
      }
    }

    if (activeTool === 'text' || activeTool === 'comment') {
      // For text and comments, prompt for content
      const content = prompt(`Enter ${activeTool}:`)
      if (content) {
        newElement.content = content
        setEditElements(prev => [...prev, newElement])
      }
    } else if (activeTool === 'highlight') {
      // For highlights, create a default rectangle
      newElement.width = 100
      newElement.height = 20
      setEditElements(prev => [...prev, newElement])
    }
  }, [activeTool, currentPage, scale])

  // Handle drawing
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (activeTool !== 'drawing') return

    setIsDrawing(true)
    const rect = canvasRef.current!.getBoundingClientRect()
    const x = (event.clientX - rect.left) / scale
    const y = (event.clientY - rect.top) / scale
    setDrawingPath([{ x, y }])
  }, [activeTool, scale])

  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || activeTool !== 'drawing') return

    const rect = canvasRef.current!.getBoundingClientRect()
    const x = (event.clientX - rect.left) / scale
    const y = (event.clientY - rect.top) / scale
    setDrawingPath(prev => [...prev, { x, y }])
  }, [isDrawing, activeTool, scale])

  const handleMouseUp = useCallback(() => {
    if (!isDrawing || activeTool !== 'drawing') return

    if (drawingPath.length > 1) {
      const newElement: EditElement = {
        id: Date.now().toString(),
        type: 'drawing',
        pageNumber: currentPage,
        x: Math.min(...drawingPath.map(p => p.x)),
        y: Math.min(...drawingPath.map(p => p.y)),
        content: JSON.stringify(drawingPath),
        style: {
          color: '#000000',
          strokeWidth: 2
        }
      }
      setEditElements(prev => [...prev, newElement])
    }

    setIsDrawing(false)
    setDrawingPath([])
  }, [isDrawing, activeTool, drawingPath, currentPage])

  // Render current page with overlays
  const renderCurrentPage = useCallback(() => {
    if (!canvasRef.current || pdfImages.length === 0) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')!
    const currentImage = pdfImages[currentPage - 1]

    if (!currentImage) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Create and draw the PDF page image
    const img = new Image()
    img.onload = () => {
      // Set canvas size
      canvas.width = img.width * scale
      canvas.height = img.height * scale
      
      // Draw the PDF page
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

      // Draw edit elements for current page
      const pageElements = editElements.filter(el => el.pageNumber === currentPage)
      
      pageElements.forEach(element => {
        ctx.save()
        
        switch (element.type) {
          case 'text':
          case 'comment':
            ctx.fillStyle = element.style?.color || '#000000'
            ctx.font = `${(element.style?.fontSize || 16) * scale}px ${element.style?.fontFamily || 'Arial'}`
            ctx.fillText(element.content || '', element.x * scale, element.y * scale)
            break
            
          case 'highlight':
            ctx.fillStyle = element.style?.backgroundColor || '#ffff0080'
            ctx.fillRect(
              element.x * scale,
              element.y * scale,
              (element.width || 100) * scale,
              (element.height || 20) * scale
            )
            break
            
          case 'drawing':
            if (element.content) {
              const path = JSON.parse(element.content) as {x: number, y: number}[]
              if (path.length > 1) {
                ctx.strokeStyle = element.style?.color || '#000000'
                ctx.lineWidth = (element.style?.strokeWidth || 2) * scale
                ctx.beginPath()
                ctx.moveTo(path[0].x * scale, path[0].y * scale)
                path.slice(1).forEach(point => {
                  ctx.lineTo(point.x * scale, point.y * scale)
                })
                ctx.stroke()
              }
            }
            break
        }
        
        ctx.restore()
      })

      // Draw current drawing path
      if (isDrawing && drawingPath.length > 1) {
        ctx.strokeStyle = '#000000'
        ctx.lineWidth = 2 * scale
        ctx.beginPath()
        ctx.moveTo(drawingPath[0].x * scale, drawingPath[0].y * scale)
        drawingPath.slice(1).forEach(point => {
          ctx.lineTo(point.x * scale, point.y * scale)
        })
        ctx.stroke()
      }
    }
    
    img.src = currentImage.imageData
  }, [pdfImages, currentPage, scale, editElements, isDrawing, drawingPath])

  // Re-render when dependencies change
  useEffect(() => {
    renderCurrentPage()
  }, [renderCurrentPage])

  const handleZoomIn = () => setScale(prev => Math.min(3.0, prev + 0.2))
  const handleZoomOut = () => setScale(prev => Math.max(0.5, prev - 0.2))

  const handleSave = async () => {
    // This will be implemented in the PDF reconstruction service
    alert('Save functionality will be implemented in the PDF reconstruction service')
  }

  const handleDownload = async () => {
    // This will be implemented in the PDF reconstruction service
    alert('Download functionality will be implemented in the PDF reconstruction service')
  }

  if (isLoading) {
    return (
      <div className="image-pdf-editor-loading">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <h3>Converting PDF to Images</h3>
          {conversionProgress && (
            <div className="conversion-progress">
              <p>{conversionProgress.message}</p>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ 
                    width: `${(conversionProgress.currentPage / conversionProgress.totalPages) * 100}%` 
                  }}
                />
              </div>
              <p>{conversionProgress.currentPage} / {conversionProgress.totalPages} pages</p>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="image-pdf-editor-error">
        <h3>Conversion Error</h3>
        <p>{error}</p>
        <button onClick={onBack} className="back-button">
          Back to Upload
        </button>
      </div>
    )
  }

  return (
    <div className="image-pdf-editor">
      <div className="editor-header">
        <div className="header-left">
          <button onClick={onBack} className="back-button">
            ← Back
          </button>
          <h2>{file.name}</h2>
          <span className="conversion-method">
            Method: {pdfToImageService.getConversionMethod()}
          </span>
        </div>
        
        <div className="header-right">
          <button onClick={handleSave} className="save-button">
            <Save size={20} />
            Save
          </button>
          <button onClick={handleDownload} className="download-button">
            <Download size={20} />
            Download
          </button>
        </div>
      </div>

      <div className="editor-toolbar">
        <div className="tool-group">
          <button 
            className={`tool-button ${activeTool === 'select' ? 'active' : ''}`}
            onClick={() => setActiveTool('select')}
            title="Select"
          >
            Select
          </button>
          <button 
            className={`tool-button ${activeTool === 'text' ? 'active' : ''}`}
            onClick={() => setActiveTool('text')}
            title="Add Text"
          >
            <Type size={20} />
          </button>
          <button 
            className={`tool-button ${activeTool === 'highlight' ? 'active' : ''}`}
            onClick={() => setActiveTool('highlight')}
            title="Highlight"
          >
            <Highlighter size={20} />
          </button>
          <button 
            className={`tool-button ${activeTool === 'comment' ? 'active' : ''}`}
            onClick={() => setActiveTool('comment')}
            title="Add Comment"
          >
            <MessageSquare size={20} />
          </button>
          <button 
            className={`tool-button ${activeTool === 'drawing' ? 'active' : ''}`}
            onClick={() => setActiveTool('drawing')}
            title="Draw"
          >
            <Pen size={20} />
          </button>
        </div>

        <div className="zoom-controls">
          <button onClick={handleZoomOut} className="zoom-button">
            <ZoomOut size={20} />
          </button>
          <span className="zoom-level">{Math.round(scale * 100)}%</span>
          <button onClick={handleZoomIn} className="zoom-button">
            <ZoomIn size={20} />
          </button>
        </div>

        <div className="page-controls">
          <button 
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage <= 1}
            className="page-button"
          >
            ← Previous
          </button>
          <span className="page-info">
            Page {currentPage} of {pdfImages.length}
          </span>
          <button 
            onClick={() => setCurrentPage(prev => Math.min(pdfImages.length, prev + 1))}
            disabled={currentPage >= pdfImages.length}
            className="page-button"
          >
            Next →
          </button>
        </div>
      </div>

      <div className="editor-content" ref={containerRef}>
        <canvas
          ref={canvasRef}
          className="pdf-canvas"
          onClick={handleCanvasClick}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          style={{ cursor: activeTool === 'drawing' ? 'crosshair' : 'default' }}
        />
      </div>
    </div>
  )
}

export default ImagePDFEditor
