import React, { useState } from 'react'
import { X, Download, Save, FileText, Settings } from 'lucide-react'

interface SaveModalProps {
  isOpen: boolean
  fileName: string
  editCount: number
  onSave: (options: SaveOptions) => void
  onCancel: () => void
}

export interface SaveOptions {
  fileName: string
  includeOriginal: boolean
  compressImages: boolean
  quality: number
}

const SaveModal: React.FC<SaveModalProps> = ({
  isOpen,
  fileName,
  editCount,
  onSave,
  onCancel
}) => {
  const [saveFileName, setSaveFileName] = useState(() => {
    const nameWithoutExt = fileName.replace(/\.pdf$/i, '')
    return `${nameWithoutExt}_edited.pdf`
  })
  const [includeOriginal, setIncludeOriginal] = useState(false)
  const [compressImages, setCompressImages] = useState(true)
  const [quality, setQuality] = useState(85)

  const handleSave = () => {
    onSave({
      fileName: saveFileName,
      includeOriginal,
      compressImages,
      quality
    })
  }

  const handleQuickSave = () => {
    onSave({
      fileName: saveFileName,
      includeOriginal: false,
      compressImages: true,
      quality: 85
    })
  }

  if (!isOpen) return null

  return (
    <>
      <div className="modal-overlay" onClick={onCancel} />
      <div className="save-modal">
        <div className="modal-header">
          <div className="header-icon">
            <Save size={20} />
            <h3>Save PDF Document</h3>
          </div>
          <button onClick={onCancel} className="close-button">
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          <div className="save-summary">
            <div className="summary-item">
              <FileText size={16} />
              <span>Original file: {fileName}</span>
            </div>
            <div className="summary-item">
              <Settings size={16} />
              <span>{editCount} edit{editCount !== 1 ? 's' : ''} applied</span>
            </div>
          </div>

          <div className="save-options">
            <div className="option-group">
              <label htmlFor="fileName">File name:</label>
              <input
                id="fileName"
                type="text"
                value={saveFileName}
                onChange={(e) => setSaveFileName(e.target.value)}
                className="file-name-input"
              />
            </div>

            <div className="option-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={includeOriginal}
                  onChange={(e) => setIncludeOriginal(e.target.checked)}
                />
                <span className="checkbox-text">
                  Include original pages (creates larger file)
                </span>
              </label>
            </div>

            <div className="option-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={compressImages}
                  onChange={(e) => setCompressImages(e.target.checked)}
                />
                <span className="checkbox-text">
                  Compress images to reduce file size
                </span>
              </label>
            </div>

            {compressImages && (
              <div className="option-group">
                <label htmlFor="quality">
                  Image quality: {quality}%
                </label>
                <input
                  id="quality"
                  type="range"
                  min="10"
                  max="100"
                  value={quality}
                  onChange={(e) => setQuality(parseInt(e.target.value))}
                  className="quality-slider"
                />
                <div className="quality-labels">
                  <span>Smaller file</span>
                  <span>Better quality</span>
                </div>
              </div>
            )}
          </div>

          <div className="save-preview">
            <h4>Preview:</h4>
            <div className="preview-info">
              <p><strong>File name:</strong> {saveFileName}</p>
              <p><strong>Estimated size:</strong> {includeOriginal ? 'Large' : 'Medium'}</p>
              <p><strong>Quality:</strong> {compressImages ? `${quality}%` : '100%'}</p>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onCancel} className="cancel-button">
            Cancel
          </button>
          <button onClick={handleQuickSave} className="quick-save-button">
            <Download size={16} />
            Quick Save
          </button>
          <button onClick={handleSave} className="save-button">
            <Save size={16} />
            Save with Options
          </button>
        </div>
      </div>
    </>
  )
}

export default SaveModal
