import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'

// Define withErrorHandling locally to avoid import issues
const withErrorHandling = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  context: string
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args)
    } catch (error) {
      console.error(`Error in ${context}:`, error)
      throw error
    }
  }
}

export interface TextElement {
  id: string
  x: number
  y: number
  text: string
  fontSize: number
  color: string
  fontFamily: string
  pageNumber: number
}

export interface HighlightElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  color: string
  pageNumber: number
}

export interface CommentElement {
  id: string
  x: number
  y: number
  text: string
  pageNumber: number
}

export interface DrawElement {
  id: string
  points: { x: number; y: number }[]
  strokeWidth: number
  color: string
  pageNumber: number
}

export interface ImageElement {
  id: string
  x: number
  y: number
  width: number
  height: number
  imageData: string
  rotation: number
  pageNumber: number
}

export type EditElement = TextElement | HighlightElement | CommentElement | DrawElement | ImageElement

export class PDFEditingService {
  private pdfDoc: PDFDocument
  private elements: EditElement[] = []

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc
  }

  async addTextElement(element: Omit<TextElement, 'id'>): Promise<string> {
    return withErrorHandling(async () => {
      const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
      const textElement: TextElement = { ...element, id }

      this.elements.push(textElement)
      await this.renderTextElement(textElement)

      return id
    }, 'PDF Text Element')()
  }

  async updateTextElement(id: string, updates: Partial<Omit<TextElement, 'id'>>): Promise<void> {
    const elementIndex = this.elements.findIndex(el => el.id === id && Object.prototype.hasOwnProperty.call(el, 'text'))
    if (elementIndex === -1) return

    const element = this.elements[elementIndex] as TextElement
    this.elements[elementIndex] = { ...element, ...updates }
    
    // Re-render the updated element
    await this.renderTextElement(this.elements[elementIndex] as TextElement)
  }

  async deleteElement(id: string): Promise<void> {
    this.elements = this.elements.filter(el => el.id !== id)
    // Note: In a real implementation, we'd need to track and remove rendered elements
    // For now, we'll regenerate the entire PDF when saving
  }

  async addHighlightElement(element: Omit<HighlightElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const highlightElement: HighlightElement = { ...element, id }
    
    this.elements.push(highlightElement)
    await this.renderHighlightElement(highlightElement)
    
    return id
  }

  async addCommentElement(element: Omit<CommentElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const commentElement: CommentElement = { ...element, id }
    
    this.elements.push(commentElement)
    await this.renderCommentElement(commentElement)
    
    return id
  }

  async addDrawElement(element: Omit<DrawElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const drawElement: DrawElement = { ...element, id }

    this.elements.push(drawElement)
    await this.renderDrawElement(drawElement)

    return id
  }

  async addImageElement(element: Omit<ImageElement, 'id'>): Promise<string> {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const imageElement: ImageElement = { ...element, id }

    this.elements.push(imageElement)
    await this.renderImageElement(imageElement)

    return id
  }

  private async renderTextElement(element: TextElement): Promise<void> {
    const pages = this.pdfDoc.getPages()
    const page = pages[element.pageNumber - 1]
    
    if (!page) return

    // Convert hex color to RGB
    const color = this.hexToRgb(element.color)
    
    // Get font (for now, use standard fonts)
    const font = await this.pdfDoc.embedFont(StandardFonts.Helvetica)
    
    // Convert coordinates (PDF coordinates start from bottom-left)
    const { height } = page.getSize()
    const pdfY = height - element.y - element.fontSize

    page.drawText(element.text, {
      x: element.x,
      y: pdfY,
      size: element.fontSize,
      font: font,
      color: rgb(color.r, color.g, color.b)
    })
  }

  private async renderHighlightElement(element: HighlightElement): Promise<void> {
    const pages = this.pdfDoc.getPages()
    const page = pages[element.pageNumber - 1]
    
    if (!page) return

    const color = this.hexToRgb(element.color)
    const { height } = page.getSize()
    const pdfY = height - element.y - element.height

    page.drawRectangle({
      x: element.x,
      y: pdfY,
      width: element.width,
      height: element.height,
      color: rgb(color.r, color.g, color.b),
      opacity: 0.3
    })
  }

  private async renderCommentElement(element: CommentElement): Promise<void> {
    const pages = this.pdfDoc.getPages()
    const page = pages[element.pageNumber - 1]
    
    if (!page) return

    const font = await this.pdfDoc.embedFont(StandardFonts.Helvetica)
    const { height } = page.getSize()
    const pdfY = height - element.y - 12

    // Draw comment icon (simple circle)
    page.drawCircle({
      x: element.x,
      y: pdfY,
      size: 8,
      color: rgb(1, 0.8, 0),
      borderColor: rgb(1, 0.6, 0),
      borderWidth: 1
    })

    // Draw comment text nearby
    page.drawText(element.text, {
      x: element.x + 20,
      y: pdfY - 6,
      size: 10,
      font: font,
      color: rgb(0, 0, 0)
    })
  }

  private async renderDrawElement(element: DrawElement): Promise<void> {
    const pages = this.pdfDoc.getPages()
    const page = pages[element.pageNumber - 1]

    if (!page || element.points.length < 2) return

    const color = this.hexToRgb(element.color)
    const { height } = page.getSize()

    // Draw lines connecting the points
    for (let i = 0; i < element.points.length - 1; i++) {
      const startPoint = element.points[i]
      const endPoint = element.points[i + 1]

      const startY = height - startPoint.y
      const endY = height - endPoint.y

      page.drawLine({
        start: { x: startPoint.x, y: startY },
        end: { x: endPoint.x, y: endY },
        thickness: element.strokeWidth,
        color: rgb(color.r, color.g, color.b)
      })
    }
  }

  private async renderImageElement(element: ImageElement): Promise<void> {
    const pages = this.pdfDoc.getPages()
    const page = pages[element.pageNumber - 1]

    if (!page) return

    try {
      // Convert data URL to bytes
      const imageBytes = this.dataUrlToBytes(element.imageData)

      // Determine image type and embed accordingly
      let image
      if (element.imageData.startsWith('data:image/png')) {
        image = await this.pdfDoc.embedPng(imageBytes)
      } else if (element.imageData.startsWith('data:image/jpeg') || element.imageData.startsWith('data:image/jpg')) {
        image = await this.pdfDoc.embedJpg(imageBytes)
      } else {
        // Try to embed as PNG by default
        image = await this.pdfDoc.embedPng(imageBytes)
      }

      const { height } = page.getSize()
      const pdfY = height - element.y - element.height

      page.drawImage(image, {
        x: element.x,
        y: pdfY,
        width: element.width,
        height: element.height
      })
    } catch (err) {
      console.error('Error rendering image:', err)
    }
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    } : { r: 0, g: 0, b: 0 }
  }

  private dataUrlToBytes(dataUrl: string): Uint8Array {
    try {
      const parts = dataUrl.split(',')
      if (parts.length !== 2) {
        throw new Error('Invalid data URL format')
      }

      const base64 = parts[1]
      if (!base64) {
        throw new Error('No base64 data found in data URL')
      }

      const binaryString = atob(base64)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      return bytes
    } catch (error) {
      console.error('Error converting data URL to bytes:', error)
      throw new Error('Failed to process image data')
    }
  }



  getElements(): EditElement[] {
    return [...this.elements]
  }

  async generateModifiedPDF(): Promise<Uint8Array> {
    // Create a copy of the original PDF
    const pdfBytes = await this.pdfDoc.save()
    const newDoc = await PDFDocument.load(pdfBytes)
    
    // Create a new editing service for the copy and apply all elements
    const newService = new PDFEditingService(newDoc)
    
    for (const element of this.elements) {
      if ('text' in element && 'fontSize' in element) {
        await newService.renderTextElement(element as TextElement)
      } else if ('width' in element && 'height' in element && !('imageData' in element)) {
        await newService.renderHighlightElement(element as HighlightElement)
      } else if ('text' in element && !('fontSize' in element)) {
        await newService.renderCommentElement(element as CommentElement)
      } else if ('points' in element) {
        await newService.renderDrawElement(element as DrawElement)
      } else if ('imageData' in element) {
        await newService.renderImageElement(element as ImageElement)
      }
    }
    
    return await newDoc.save()
  }
}
