import React, { useState, useEffect, useCallback } from 'react'
import { AlertTriangle, Download, RefreshCw, Info } from 'lucide-react'
import SimplePDFViewer from './SimplePDFViewer'
import CanvasPDFViewer from './CanvasPDFViewer'
import PDFViewer from './PDFViewer'

interface ProgressivePDFViewerProps {
  file: File
  onPageClick?: (pageNumber: number, x: number, y: number) => void
}

type ViewerType = 'download' | 'simple' | 'canvas' | 'advanced'
type ViewerStatus = 'untested' | 'testing' | 'success' | 'failed'

interface ViewerInfo {
  type: ViewerType
  name: string
  description: string
  status: ViewerStatus
  error?: string
}

const ProgressivePDFViewer: React.FC<ProgressivePDFViewerProps> = ({ file, onPageClick }) => {
  const [currentViewer, setCurrentViewer] = useState<ViewerType>('simple')
  const [isAutoProgressing, setIsAutoProgressing] = useState(true)
  const [debugLogs, setDebugLogs] = useState<string[]>([])
  const [viewers, setViewers] = useState<ViewerInfo[]>([
    { type: 'download', name: 'Download Only', description: 'Always works - download to view', status: 'success' },
    { type: 'simple', name: 'Browser Native', description: 'Uses browser PDF plugin', status: 'testing' },
    { type: 'canvas', name: 'Canvas Renderer', description: 'PDF.js with canvas rendering', status: 'untested' },
    { type: 'advanced', name: 'React PDF', description: 'Full-featured react-pdf viewer', status: 'untested' }
  ])

  // Debug logging
  const addDebugLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    const logMessage = `[${timestamp}] ${message}`
    console.log(`ProgressivePDFViewer: ${logMessage}`)
    setDebugLogs(prev => [...prev.slice(-14), logMessage])
  }, [])

  // Update viewer status
  const updateViewerStatus = useCallback((type: ViewerType, status: ViewerStatus, error?: string) => {
    setViewers(prev => prev.map(viewer => 
      viewer.type === type 
        ? { ...viewer, status, error }
        : viewer
    ))
    addDebugLog(`Viewer ${type} status: ${status}${error ? ` - ${error}` : ''}`)
  }, [addDebugLog])

  // Auto-progress to next viewer on failure
  const progressToNextViewer = useCallback(() => {
    if (!isAutoProgressing) return

    const viewerOrder: ViewerType[] = ['simple', 'canvas', 'advanced', 'download']
    const currentIndex = viewerOrder.indexOf(currentViewer)
    
    if (currentIndex < viewerOrder.length - 1) {
      const nextViewer = viewerOrder[currentIndex + 1]
      addDebugLog(`Auto-progressing from ${currentViewer} to ${nextViewer}`)
      setCurrentViewer(nextViewer)
      updateViewerStatus(nextViewer, 'testing')
    } else {
      addDebugLog('Reached final fallback (download)')
      setIsAutoProgressing(false)
    }
  }, [currentViewer, isAutoProgressing, addDebugLog, updateViewerStatus])

  // Handle viewer success
  const handleViewerSuccess = useCallback((type: ViewerType) => {
    updateViewerStatus(type, 'success')
    setIsAutoProgressing(false) // Stop auto-progression on success
  }, [updateViewerStatus])

  // Handle viewer failure
  const handleViewerFailure = useCallback((type: ViewerType, error: string) => {
    updateViewerStatus(type, 'failed', error)
    
    if (isAutoProgressing) {
      setTimeout(() => {
        progressToNextViewer()
      }, 2000) // Wait 2 seconds before trying next viewer
    }
  }, [updateViewerStatus, isAutoProgressing, progressToNextViewer])

  // Initialize
  useEffect(() => {
    addDebugLog(`Initializing progressive PDF viewer for: ${file.name}`)
    updateViewerStatus('simple', 'testing')
  }, [file, addDebugLog, updateViewerStatus])

  // Manual viewer selection
  const selectViewer = useCallback((type: ViewerType) => {
    addDebugLog(`Manually selecting viewer: ${type}`)
    setCurrentViewer(type)
    setIsAutoProgressing(false)
    updateViewerStatus(type, 'testing')
  }, [addDebugLog, updateViewerStatus])

  // Download handler
  const handleDownload = useCallback(() => {
    const url = URL.createObjectURL(file)
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    addDebugLog('File downloaded')
  }, [file, addDebugLog])

  // Render download-only mode
  if (currentViewer === 'download') {
    return (
      <div className="progressive-pdf-viewer">
        <div className="viewer-status-bar">
          <Info size={16} />
          <span>Download Mode - PDF viewing not available in browser</span>
        </div>

        <div className="pdf-download-mode">
          <div className="download-content">
            <AlertTriangle size={48} className="warning-icon" />
            <h3>PDF Viewing Not Available</h3>
            <p>This PDF cannot be displayed in your browser. Please download it to view.</p>
            
            <div className="file-info">
              <p><strong>File:</strong> {file.name}</p>
              <p><strong>Size:</strong> {(file.size / 1024).toFixed(1)} KB</p>
              <p><strong>Type:</strong> {file.type}</p>
            </div>

            <div className="download-actions">
              <button onClick={handleDownload} className="download-button">
                <Download size={20} />
                Download PDF
              </button>
            </div>

            <div className="viewer-selection">
              <p>Try a different viewer:</p>
              <div className="viewer-buttons">
                {viewers.filter(v => v.type !== 'download').map(viewer => (
                  <button
                    key={viewer.type}
                    onClick={() => selectViewer(viewer.type)}
                    className={`viewer-button ${viewer.status}`}
                    disabled={viewer.status === 'testing'}
                  >
                    {viewer.name}
                    {viewer.status === 'failed' && ' ❌'}
                    {viewer.status === 'success' && ' ✅'}
                    {viewer.status === 'testing' && ' ⏳'}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <div className="debug-panel">
            <details>
              <summary>Debug Information ({debugLogs.length} logs)</summary>
              <div className="debug-info">
                {debugLogs.map((log, index) => (
                  <div key={index} className="debug-log">{log}</div>
                ))}
              </div>
            </details>
          </div>
        )}
      </div>
    )
  }

  // Render active viewer
  return (
    <div className="progressive-pdf-viewer">
      {/* Status Bar */}
      <div className="viewer-status-bar">
        <div className="current-viewer-info">
          <span className="viewer-name">
            {viewers.find(v => v.type === currentViewer)?.name}
          </span>
          <span className="viewer-description">
            {viewers.find(v => v.type === currentViewer)?.description}
          </span>
        </div>
        
        {isAutoProgressing && (
          <div className="auto-progress-indicator">
            <RefreshCw size={16} className="spinning" />
            <span>Auto-selecting best viewer...</span>
          </div>
        )}
      </div>

      {/* Viewer Selection */}
      <div className="viewer-selection-bar">
        {viewers.map(viewer => (
          <button
            key={viewer.type}
            onClick={() => selectViewer(viewer.type)}
            className={`viewer-tab ${currentViewer === viewer.type ? 'active' : ''} ${viewer.status}`}
            disabled={viewer.status === 'testing'}
            title={viewer.error || viewer.description}
          >
            {viewer.name}
            {viewer.status === 'failed' && ' ❌'}
            {viewer.status === 'success' && ' ✅'}
            {viewer.status === 'testing' && ' ⏳'}
          </button>
        ))}
      </div>

      {/* Active Viewer */}
      <div className="active-viewer">
        {currentViewer === 'simple' && (
          <SimplePDFViewer
            file={file}
            onPageClick={onPageClick}
          />
        )}
        {currentViewer === 'canvas' && (
          <CanvasPDFViewer
            file={file}
            onPageClick={onPageClick}
          />
        )}
        {currentViewer === 'advanced' && (
          <PDFViewer
            file={file}
            onPageClick={onPageClick}
          />
        )}
      </div>

      {process.env.NODE_ENV === 'development' && (
        <div className="debug-panel">
          <details>
            <summary>Debug Information ({debugLogs.length} logs)</summary>
            <div className="debug-info">
              {debugLogs.map((log, index) => (
                <div key={index} className="debug-log">{log}</div>
              ))}
            </div>
          </details>
        </div>
      )}
    </div>
  )
}

export default ProgressivePDFViewer
